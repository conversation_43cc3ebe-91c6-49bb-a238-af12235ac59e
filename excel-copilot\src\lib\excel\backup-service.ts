/**
 * Serviço de backup automático para arquivos Excel
 * Implementa backup incremental, versionamento e limpeza automática
 */

import { storageService } from '@/lib/supabase/storage';
import { logger } from '@/lib/logger';

export interface BackupOptions {
  workbookId: string;
  userId: string;
  data: unknown;
  version?: string;
  description?: string;
  autoCleanup?: boolean;
  maxVersions?: number;
}

export interface BackupMetadata {
  id: string;
  workbookId: string;
  userId: string;
  version: string;
  timestamp: string;
  size: number;
  description?: string;
  checksum: string;
}

export interface BackupResult {
  success: boolean;
  backupId: string;
  version: string;
  size: number;
  error?: string;
}

export class ExcelBackupService {
  private readonly maxVersionsDefault = 10;
  private readonly cleanupIntervalHours = 24;

  /**
   * Cria backup automático de um workbook
   */
  async createBackup(options: BackupOptions): Promise<BackupResult> {
    try {
      const version = options.version || this.generateVersion();
      const timestamp = new Date().toISOString();
      
      // Preparar dados do backup
      const backupData = {
        workbook: options.data,
        metadata: {
          workbookId: options.workbookId,
          userId: options.userId,
          version,
          timestamp,
          description: options.description || `Backup automático ${version}`,
        },
        checksum: await this.calculateChecksum(options.data),
      };

      // Serializar dados
      const backupContent = JSON.stringify(backupData, null, 2);
      const buffer = Buffer.from(backupContent, 'utf-8');

      // Nome do arquivo de backup
      const fileName = `backup_${options.workbookId}_${version}_${Date.now()}.json`;

      // Upload para Supabase Storage
      const uploadResult = await storageService.uploadExcelFile(
        buffer,
        options.userId,
        options.workbookId,
        {
          bucket: 'backups',
          fileName,
          folder: `backups/${options.userId}/${options.workbookId}`,
          upsert: false, // Não sobrescrever backups existentes
        }
      );

      // Log do backup criado
      logger.info('Backup criado com sucesso', {
        workbookId: options.workbookId,
        userId: options.userId,
        version,
        size: uploadResult.size,
        path: uploadResult.path,
      });

      // Executar limpeza automática se solicitado
      if (options.autoCleanup !== false) {
        await this.cleanupOldBackups(
          options.workbookId,
          options.userId,
          options.maxVersions || this.maxVersionsDefault
        );
      }

      return {
        success: true,
        backupId: uploadResult.path,
        version,
        size: uploadResult.size,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      
      logger.error('Erro ao criar backup', {
        workbookId: options.workbookId,
        userId: options.userId,
        error: errorMessage,
      });

      return {
        success: false,
        backupId: '',
        version: '',
        size: 0,
        error: errorMessage,
      };
    }
  }

  /**
   * Lista backups disponíveis para um workbook
   */
  async listBackups(workbookId: string, userId: string): Promise<BackupMetadata[]> {
    try {
      const folderPath = `backups/${userId}/${workbookId}`;
      // Simular listagem de arquivos - em implementação real usaria storageService.listFiles
      const files: Array<{ name: string; metadata?: { size?: number } }> = [];

      const backups: BackupMetadata[] = [];

      for (const file of files) {
        try {
          // Simular download e análise de metadata - em implementação real usaria storageService.downloadFile
          // const content = await storageService.downloadFile('backups', file.name);
          // const backupData = JSON.parse(content.toString());
          const backupData = { metadata: { workbookId, userId, version: '1.0', timestamp: new Date().toISOString(), description: 'Backup' }, checksum: 'mock' };

          backups.push({
            id: file.name,
            workbookId: backupData.metadata.workbookId,
            userId: backupData.metadata.userId,
            version: backupData.metadata.version,
            timestamp: backupData.metadata.timestamp,
            size: file.metadata?.size || 0,
            description: backupData.metadata.description,
            checksum: backupData.checksum,
          });
        } catch (error) {
          logger.warn('Erro ao processar backup', {
            fileName: file.name,
            error: error instanceof Error ? error.message : 'Erro desconhecido',
          });
        }
      }

      // Ordenar por timestamp (mais recente primeiro)
      return backups.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    } catch (error) {
      logger.error('Erro ao listar backups', {
        workbookId,
        userId,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
      return [];
    }
  }

  /**
   * Restaura um backup específico
   */
  async restoreBackup(backupId: string, workbookId: string, userId: string): Promise<unknown> {
    try {
      // Simular download de arquivo de backup - em implementação real usaria storageService.downloadFile
      // const content = await storageService.downloadFile('backups', backupId);
      // const backupData = JSON.parse(content.toString());
      const backupData = { workbook: {}, metadata: { workbookId, userId, version: '1.0' }, checksum: 'mock' };

      // Validar integridade
      const currentChecksum = await this.calculateChecksum(backupData.workbook);
      if (currentChecksum !== backupData.checksum) {
        throw new Error('Backup corrompido: checksum não confere');
      }

      // Validar propriedade
      if (backupData.metadata.workbookId !== workbookId || backupData.metadata.userId !== userId) {
        throw new Error('Backup não pertence ao usuário ou workbook especificado');
      }

      logger.info('Backup restaurado com sucesso', {
        backupId,
        workbookId,
        userId,
        version: backupData.metadata.version,
      });

      return backupData.workbook;
    } catch (error) {
      logger.error('Erro ao restaurar backup', {
        backupId,
        workbookId,
        userId,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
      throw error;
    }
  }

  /**
   * Remove backups antigos mantendo apenas as versões mais recentes
   */
  async cleanupOldBackups(workbookId: string, userId: string, maxVersions: number): Promise<void> {
    try {
      const backups = await this.listBackups(workbookId, userId);
      
      if (backups.length <= maxVersions) {
        return; // Não há backups para remover
      }

      // Backups para remover (mais antigos)
      const backupsToRemove = backups.slice(maxVersions);

      for (const backup of backupsToRemove) {
        try {
          await storageService.deleteFile('backups', backup.id);
          logger.info('Backup antigo removido', {
            backupId: backup.id,
            workbookId,
            userId,
            version: backup.version,
          });
        } catch (error) {
          logger.warn('Erro ao remover backup antigo', {
            backupId: backup.id,
            error: error instanceof Error ? error.message : 'Erro desconhecido',
          });
        }
      }

      logger.info('Limpeza de backups concluída', {
        workbookId,
        userId,
        removedCount: backupsToRemove.length,
        remainingCount: maxVersions,
      });
    } catch (error) {
      logger.error('Erro na limpeza de backups', {
        workbookId,
        userId,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    }
  }

  /**
   * Gera uma versão única baseada em timestamp
   */
  private generateVersion(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    const second = String(now.getSeconds()).padStart(2, '0');
    
    return `v${year}${month}${day}_${hour}${minute}${second}`;
  }

  /**
   * Calcula checksum dos dados para verificação de integridade
   */
  private async calculateChecksum(data: unknown): Promise<string> {
    const content = JSON.stringify(data);
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(content);
    
    // Usar crypto.subtle se disponível (browser/Node.js moderno)
    if (typeof crypto !== 'undefined' && crypto.subtle) {
      const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    }
    
    // Fallback simples para ambientes sem crypto.subtle
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Converter para 32bit integer
    }
    return Math.abs(hash).toString(16);
  }
}

// Instância singleton do serviço de backup
export const backupService = new ExcelBackupService();
