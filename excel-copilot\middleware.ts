import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

// Rate limiting em memória (em produção, usar Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number; blocked?: boolean; blockUntil?: number }>();

// Configurações de rate limiting
const RATE_LIMITS = {
  GLOBAL: { maxRequests: 1000, windowMs: 60 * 60 * 1000 }, // 1000 req/hora
  AUTH: { maxRequests: 10, windowMs: 15 * 60 * 1000 }, // 10 req/15min para auth
  API: { maxRequests: 100, windowMs: 60 * 1000 }, // 100 req/min para APIs
  SENSITIVE: { maxRequests: 5, windowMs: 5 * 60 * 1000 }, // 5 req/5min para operações sensíveis
};

/**
 * Verifica rate limiting para um IP e tipo de operação
 */
function checkRateLimit(ip: string, type: keyof typeof RATE_LIMITS): { allowed: boolean; remaining: number; resetTime: number } {
  const config = RATE_LIMITS[type];
  const key = `${type}:${ip}`;
  const now = Date.now();

  let record = rateLimitStore.get(key);

  // Se não existe ou expirou, criar novo
  if (!record || now > record.resetTime) {
    record = {
      count: 1,
      resetTime: now + config.windowMs,
    };
    rateLimitStore.set(key, record);
    return { allowed: true, remaining: config.maxRequests - 1, resetTime: record.resetTime };
  }

  // Se está bloqueado, verificar se ainda está no período de bloqueio
  if (record.blocked && record.blockUntil && now < record.blockUntil) {
    return { allowed: false, remaining: 0, resetTime: record.blockUntil };
  }

  // Incrementar contador
  record.count++;

  // Verificar se excedeu o limite
  if (record.count > config.maxRequests) {
    // Bloquear por 15 minutos se exceder muito o limite
    if (record.count > config.maxRequests * 2) {
      record.blocked = true;
      record.blockUntil = now + (15 * 60 * 1000); // 15 minutos
    }
    return { allowed: false, remaining: 0, resetTime: record.resetTime };
  }

  return { allowed: true, remaining: config.maxRequests - record.count, resetTime: record.resetTime };
}

/**
 * Middleware principal do Excel Copilot
 * Aplica rate limiting e verificações de segurança globalmente
 */
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';

  // Aplicar apenas para rotas de API
  if (pathname.startsWith('/api/')) {
    // Rate limiting global
    const globalLimit = checkRateLimit(ip, 'GLOBAL');
    if (!globalLimit.allowed) {
      return NextResponse.json(
        {
          error: 'Rate limit exceeded',
          message: 'Muitas requisições. Tente novamente mais tarde.',
          retryAfter: Math.ceil((globalLimit.resetTime - Date.now()) / 1000)
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': String(RATE_LIMITS.GLOBAL.maxRequests),
            'X-RateLimit-Remaining': String(globalLimit.remaining),
            'X-RateLimit-Reset': String(Math.floor(globalLimit.resetTime / 1000)),
            'Retry-After': String(Math.ceil((globalLimit.resetTime - Date.now()) / 1000)),
          }
        }
      );
    }

    // Rate limiting específico para autenticação
    if (pathname.startsWith('/api/auth/')) {
      const authLimit = checkRateLimit(ip, 'AUTH');
      if (!authLimit.allowed) {
        return NextResponse.json(
          {
            error: 'Authentication rate limit exceeded',
            message: 'Muitas tentativas de autenticação. Aguarde antes de tentar novamente.',
            retryAfter: Math.ceil((authLimit.resetTime - Date.now()) / 1000)
          },
          {
            status: 429,
            headers: {
              'X-RateLimit-Limit': String(RATE_LIMITS.AUTH.maxRequests),
              'X-RateLimit-Remaining': String(authLimit.remaining),
              'X-RateLimit-Reset': String(Math.floor(authLimit.resetTime / 1000)),
              'Retry-After': String(Math.ceil((authLimit.resetTime - Date.now()) / 1000)),
            }
          }
        );
      }
    }

    // Rate limiting para operações sensíveis
    if (
      pathname.startsWith('/api/payment/') ||
      pathname.startsWith('/api/admin/') ||
      pathname.startsWith('/api/webhooks/')
    ) {
      const sensitiveLimit = checkRateLimit(ip, 'SENSITIVE');
      if (!sensitiveLimit.allowed) {
        return NextResponse.json(
          {
            error: 'Sensitive operation rate limit exceeded',
            message: 'Muitas tentativas em operações sensíveis. Acesso temporariamente restrito.',
            retryAfter: Math.ceil((sensitiveLimit.resetTime - Date.now()) / 1000)
          },
          {
            status: 429,
            headers: {
              'X-RateLimit-Limit': String(RATE_LIMITS.SENSITIVE.maxRequests),
              'X-RateLimit-Remaining': String(sensitiveLimit.remaining),
              'X-RateLimit-Reset': String(Math.floor(sensitiveLimit.resetTime / 1000)),
              'Retry-After': String(Math.ceil((sensitiveLimit.resetTime - Date.now()) / 1000)),
            }
          }
        );
      }
    }

    // Para APIs que requerem autenticação
    if (
      pathname.startsWith('/api/workbooks') ||
      pathname.startsWith('/api/chat') ||
      pathname.startsWith('/api/workbook/save')
    ) {
      // Verificar se usuário está autenticado
      const token = await getToken({
        req: request,
        secret: process.env.NEXTAUTH_SECRET || process.env.AUTH_NEXTAUTH_SECRET,
      });

      if (!token && !pathname.includes('/shared')) {
        return NextResponse.json(
          { error: 'Não autorizado. Faça login para continuar.' },
          { status: 401 }
        );
      }

      // Rate limiting para APIs autenticadas
      const apiLimit = checkRateLimit(ip, 'API');
      if (!apiLimit.allowed) {
        return NextResponse.json(
          {
            error: 'API rate limit exceeded',
            message: 'Muitas requisições à API. Aguarde antes de continuar.',
            retryAfter: Math.ceil((apiLimit.resetTime - Date.now()) / 1000)
          },
          {
            status: 429,
            headers: {
              'X-RateLimit-Limit': String(RATE_LIMITS.API.maxRequests),
              'X-RateLimit-Remaining': String(apiLimit.remaining),
              'X-RateLimit-Reset': String(Math.floor(apiLimit.resetTime / 1000)),
              'Retry-After': String(Math.ceil((apiLimit.resetTime - Date.now()) / 1000)),
            }
          }
        );
      }

      // Adicionar headers de segurança
      const response = NextResponse.next();
      response.headers.set('X-Content-Type-Options', 'nosniff');
      response.headers.set('X-Frame-Options', 'DENY');
      response.headers.set('X-XSS-Protection', '1; mode=block');
      response.headers.set('X-RateLimit-Limit', String(RATE_LIMITS.API.maxRequests));
      response.headers.set('X-RateLimit-Remaining', String(apiLimit.remaining));
      response.headers.set('X-RateLimit-Reset', String(Math.floor(apiLimit.resetTime / 1000)));

      return response;
    }

    // Headers de segurança para todas as APIs
    const response = NextResponse.next();
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('X-RateLimit-Limit', String(RATE_LIMITS.GLOBAL.maxRequests));
    response.headers.set('X-RateLimit-Remaining', String(globalLimit.remaining));
    response.headers.set('X-RateLimit-Reset', String(Math.floor(globalLimit.resetTime / 1000)));

    return response;
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/api/:path*', '/((?!_next/static|_next/image|favicon.ico).*)'],
};
