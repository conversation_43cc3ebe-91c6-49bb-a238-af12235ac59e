(()=>{var e={};e.id=813,e.ids=[813],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},94007:e=>{"use strict";e.exports=require("@prisma/client")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},32081:e=>{"use strict";e.exports=require("child_process")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},98188:e=>{"use strict";e.exports=require("module")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},71267:e=>{"use strict";e.exports=require("worker_threads")},55601:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),s(5980),s(65675),s(12523);var t=s(23191),a=s(88716),o=s(37922),n=s.n(o),i=s(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(r,l);let d=["",{children:["dashboard",{children:["account",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5980)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\dashboard\\account\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,65675)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\not-found.tsx"]}],c=["C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\dashboard\\account\\page.tsx"],u="/dashboard/account/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/account/page",pathname:"/dashboard/account",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6123:(e,r,s)=>{Promise.resolve().then(s.bind(s,80507))},95396:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(76557).Z)("BarChart",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]])},7027:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(76557).Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]])},75290:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},58038:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(76557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},80507:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>g});var t=s(10326),a=s(75290),o=s(77109),n=s(17577),i=s(85999),l=s(58038),d=s(95396),c=s(28916),u=s(7027),p=s(3634),m=s(91664),x=s(29752),f=s(33269),h=s(96833);function v({subscription:e,onCreatePortalSession:r}){let[s,a]=(0,n.useState)(!1),o=Math.min(Math.round(e.apiCallsUsed/e.apiCallsLimit*100),100),v=e=>e?new Intl.DateTimeFormat("pt-BR",{day:"numeric",month:"long",year:"numeric"}).format(new Date(e)):"N/A",g=async()=>{try{a(!0);let e=await r();if(e)window.location.href=e;else throw Error("N\xe3o foi poss\xedvel obter o link do portal")}catch(e){console.error("Erro ao abrir portal de assinatura:",e),i.toast.error("N\xe3o foi poss\xedvel acessar o portal de faturamento. Tente novamente mais tarde.")}finally{a(!1)}};return(0,t.jsxs)(x.Zb,{className:"border-primary/30",children:[(0,t.jsxs)(x.Ol,{className:"pb-2",children:[(0,t.jsxs)(x.ll,{className:"flex items-center gap-2",children:[t.jsx(l.Z,{className:"free"!==e.plan?"text-primary":"text-muted-foreground",size:18}),(0,h.Nt)(e.plan),"past_due"===e.status&&t.jsx("span",{className:"text-xs font-normal ml-2 py-0.5 px-2 rounded bg-amber-100 text-amber-800 dark:bg-amber-900/60 dark:text-amber-200",children:"Pagamento pendente"}),e.cancelAtPeriodEnd&&t.jsx("span",{className:"text-xs font-normal ml-2 py-0.5 px-2 rounded bg-rose-100 text-rose-800 dark:bg-rose-900/60 dark:text-rose-200",children:"Cancelamento agendado"})]}),t.jsx(x.SZ,{children:"free"===e.id?"Plano gratuito com recursos b\xe1sicos":"Acesso a todos os recursos pro"})]}),(0,t.jsxs)(x.aY,{className:"pb-2 space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1.5 font-medium",children:[t.jsx(d.Z,{size:16,className:"text-muted-foreground"}),t.jsx("span",{children:"Uso da API"})]}),(0,t.jsxs)("span",{children:[e.apiCallsUsed," / ",e.apiCallsLimit," chamadas"]})]}),t.jsx(f.E,{value:o,className:"h-2"}),t.jsx("p",{className:"text-xs text-muted-foreground",children:o>=80?"Voc\xea est\xe1 pr\xf3ximo do seu limite mensal.":"Seu uso est\xe1 dentro do esperado."})]}),"free"!==e.id&&(0,t.jsxs)("div",{className:"space-y-4 pt-2",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-muted-foreground mb-1",children:"Status"}),(0,t.jsxs)("p",{className:"font-medium",children:["active"===e.status&&"Ativo","trialing"===e.status&&"Em trial","past_due"===e.status&&"Pagamento pendente","canceled"===e.status&&"Cancelado","incomplete"===e.status&&"Incompleto"]})]}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-muted-foreground mb-1",children:"Pr\xf3xima cobran\xe7a"}),t.jsx("p",{className:"font-medium",children:v(e.currentPeriodEnd)})]})]}),e.cancelAtPeriodEnd&&t.jsx("div",{className:"rounded-md bg-amber-50 dark:bg-amber-950/30 p-3 text-sm text-amber-800 dark:text-amber-200 border border-amber-200 dark:border-amber-800/40",children:(0,t.jsxs)("p",{children:["Sua assinatura ser\xe1 encerrada em ",v(e.currentPeriodEnd),"."]})})]})]}),t.jsx(x.eW,{className:"pt-2",children:"free"!==e.id?t.jsx(m.Button,{onClick:()=>g(),className:"w-full",disabled:s,children:s?"Carregando...":(0,t.jsxs)(t.Fragment,{children:[t.jsx(c.Z,{className:"mr-2 h-4 w-4"}),"Gerenciar Assinatura",t.jsx(u.Z,{className:"ml-2 h-3.5 w-3.5"})]})}):(0,t.jsxs)(m.Button,{variant:"outline",className:"w-full",onClick:()=>window.location.href="/pricing",children:[t.jsx(p.Z,{className:"mr-2 h-4 w-4"}),"Fazer Upgrade"]})})]})}function g(){let{data:e}=(0,o.useSession)(),[r,s]=(0,n.useState)(null),[l,d]=(0,n.useState)(!0),c=async()=>{try{let e=await fetch("/api/billing/customer-portal",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({returnUrl:`${window.location.origin}/dashboard/account`})}),r=await e.json();if(!e.ok)throw Error(r.error||"Erro ao acessar o portal");return r.url}catch{return i.toast.error("N\xe3o foi poss\xedvel acessar o portal de gerenciamento"),null}};return l?t.jsx("div",{className:"flex items-center justify-center h-[calc(100vh-200px)]",children:(0,t.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[t.jsx(a.Z,{className:"h-8 w-8 animate-spin text-primary"}),t.jsx("p",{className:"text-muted-foreground",children:"Carregando informa\xe7\xf5es da conta..."})]})}):(0,t.jsxs)("div",{className:"container mx-auto px-4 py-12 max-w-4xl",children:[t.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Sua Conta"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[t.jsx("div",{className:"space-y-6",children:(0,t.jsxs)(x.Zb,{children:[(0,t.jsxs)(x.Ol,{children:[t.jsx(x.ll,{children:"Perfil"}),t.jsx(x.SZ,{children:"Suas informa\xe7\xf5es pessoais"})]}),t.jsx(x.aY,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:"Nome"}),t.jsx("p",{children:e?.user?.name||"N\xe3o informado"})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:"Email"}),t.jsx("p",{children:e?.user?.email||"N\xe3o informado"})]})]})})]})}),(0,t.jsxs)("div",{className:"md:col-span-2",children:[t.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Sua Assinatura"}),r&&t.jsx(v,{subscription:r,onCreatePortalSession:c})]})]})]})}},29752:(e,r,s)=>{"use strict";s.d(r,{Ol:()=>d,SZ:()=>u,Zb:()=>l,aY:()=>p,eW:()=>m,ll:()=>c});var t=s(10326),a=s(31722),o=s(17577),n=s(45365),i=s(51223);let l=(0,o.forwardRef)(({className:e,children:r,hoverable:s=!1,variant:o="default",noPadding:l=!1,animated:d=!1,...c},u)=>{let p=(0,i.cn)("rounded-xl border shadow-sm",{"p-6":!l,"hover:shadow-md hover:-translate-y-1 transition-all duration-200":s&&!d,"border-border bg-card":"default"===o,"border-border/50 bg-transparent":"outline"===o,"bg-card/90 backdrop-blur-md border-border/50":"glass"===o,"bg-gradient-primary text-primary-foreground border-none":"gradient"===o},e);return d?t.jsx(a.E.div,{ref:u,className:p,...(0,n.Ph)("card"),whileHover:s?n.q.hover:void 0,whileTap:s?n.q.tap:void 0,...c,children:r}):t.jsx("div",{ref:u,className:p,...c,children:r})});l.displayName="Card";let d=(0,o.forwardRef)(({className:e,...r},s)=>t.jsx("div",{ref:s,className:(0,i.cn)("mb-4 flex flex-col space-y-1.5",e),...r}));d.displayName="CardHeader";let c=(0,o.forwardRef)(({className:e,...r},s)=>t.jsx("h3",{ref:s,className:(0,i.cn)("text-xl font-semibold leading-none tracking-tight",e),...r}));c.displayName="CardTitle";let u=(0,o.forwardRef)(({className:e,...r},s)=>t.jsx("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));u.displayName="CardDescription";let p=(0,o.forwardRef)(({className:e,...r},s)=>t.jsx("div",{ref:s,className:(0,i.cn)("card-content",e),...r}));p.displayName="CardContent";let m=(0,o.forwardRef)(({className:e,...r},s)=>t.jsx("div",{ref:s,className:(0,i.cn)("flex items-center pt-4 mt-auto",e),...r}));m.displayName="CardFooter"},33269:(e,r,s)=>{"use strict";s.d(r,{E:()=>i});var t=s(10326),a=s(87273),o=s(17577),n=s(51223);let i=o.forwardRef(({className:e,value:r,...s},o)=>t.jsx(a.fC,{ref:o,className:(0,n.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...s,children:t.jsx(a.z$,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(r||0)}%)`}})}));i.displayName=a.fC.displayName},96833:(e,r,s)=>{"use strict";s.d(r,{Nt:()=>n,Xf:()=>a}),s(89244);var t=s(89275);let a={FREE:"free",PRO_MONTHLY:"pro_monthly",PRO_ANNUAL:"pro_annual"};a.FREE,a.PRO_MONTHLY,a.PRO_ANNUAL;let o=process.env.STRIPE_SECRET_KEY||"";function n(e){switch(e){case a.FREE:return"Gr\xe1tis";case a.PRO_MONTHLY:return"Pro Mensal";case a.PRO_ANNUAL:return"Pro Anual";default:return"Desconhecido"}}process.env.STRIPE_WEBHOOK_SECRET,o&&new t.Z(o,{apiVersion:"2023-10-16",appInfo:{name:"Excel Copilot",version:"1.0.0"}})},38238:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"ReflectAdapter",{enumerable:!0,get:function(){return s}});class s{static get(e,r,s){let t=Reflect.get(e,r,s);return"function"==typeof t?t.bind(e):t}static set(e,r,s,t){return Reflect.set(e,r,s,t)}static has(e,r){return Reflect.has(e,r)}static deleteProperty(e,r){return Reflect.deleteProperty(e,r)}}},5980:(e,r,s)=>{"use strict";s.r(r),s.d(r,{$$typeof:()=>n,__esModule:()=>o,default:()=>i});var t=s(68570);let a=(0,t.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\app\dashboard\account\page.tsx`),{__esModule:o,$$typeof:n}=a;a.default;let i=(0,t.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\app\dashboard\account\page.tsx#default`)},87273:(e,r,s)=>{"use strict";s.d(r,{fC:()=>N,z$:()=>j});var t=s(17577),a=s(93095),o=s(45226),n=s(10326),i="Progress",[l,d]=(0,a.b)(i),[c,u]=l(i),p=t.forwardRef((e,r)=>{var s,t;let{__scopeProgress:a,value:i=null,max:l,getValueLabel:d=f,...u}=e;(l||0===l)&&!g(l)&&console.error((s=`${l}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let p=g(l)?l:100;null===i||b(i,p)||console.error((t=`${i}`,`Invalid prop \`value\` of value \`${t}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let m=b(i,p)?i:null,x=v(m)?d(m,p):void 0;return(0,n.jsx)(c,{scope:a,value:m,max:p,children:(0,n.jsx)(o.WV.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":v(m)?m:void 0,"aria-valuetext":x,role:"progressbar","data-state":h(m,p),"data-value":m??void 0,"data-max":p,...u,ref:r})})});p.displayName=i;var m="ProgressIndicator",x=t.forwardRef((e,r)=>{let{__scopeProgress:s,...t}=e,a=u(m,s);return(0,n.jsx)(o.WV.div,{"data-state":h(a.value,a.max),"data-value":a.value??void 0,"data-max":a.max,...t,ref:r})});function f(e,r){return`${Math.round(e/r*100)}%`}function h(e,r){return null==e?"indeterminate":e===r?"complete":"loading"}function v(e){return"number"==typeof e}function g(e){return v(e)&&!isNaN(e)&&e>0}function b(e,r){return v(e)&&!isNaN(e)&&e<=r&&e>=0}x.displayName=m;var N=p,j=x}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[8948,9557,7410,86,7915,5999,4002,2972,4433,6841],()=>s(55601));module.exports=t})();