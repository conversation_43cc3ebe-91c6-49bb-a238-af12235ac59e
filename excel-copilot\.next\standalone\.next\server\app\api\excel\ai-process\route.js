"use strict";(()=>{var e={};e.id=6247,e.ids=[6247],e.modules={53524:e=>{e.exports=require("@prisma/client")},57641:e=>{e.exports=require("exceljs")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},32081:e=>{e.exports=require("child_process")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},14039:(e,a,r)=>{r.r(a),r.d(a,{originalPathname:()=>H,patchFetch:()=>G,requestAsyncStorage:()=>z,routeModule:()=>k,serverHooks:()=>j,staticGenerationAsyncStorage:()=>B});var s,o,t,n,i,l,c={};r.r(c),r.d(c,{POST:()=>P});var d=r(49303),u=r(88716),m=r(60670),p=r(43895);class A{constructor(e={},a=!0){this.context={activeSheet:e.activeSheet||"Sheet1",headers:e.headers||[],selection:e.selection||"A1",recentOperations:e.recentOperations||[]},this.useRealAI=a}async processQuery(e){try{let a=this.preprocessQuery(e),r=this.buildPrompt(a),s=await g(),o=await s.sendMessage(r,{context:JSON.stringify(this.context),useMock:!this.useRealAI});return this.parseAIResponse(o)}catch(e){return console.error("Erro ao processar query:",e),{operations:[],error:`Erro ao processar: ${e instanceof Error?e.message:String(e)}`,success:!1,message:"Falha ao processar query com IA"}}}preprocessQuery(e){return e.replace(/\bform\./g,"f\xf3rmula").replace(/\bcol\./g,"coluna").replace(/\btab\./g,"tabela").replace(/\bgraf\./g,"gr\xe1fico").replace(/\bcel\./g,"c\xe9lula").replace(/\bfunc\./g,"fun\xe7\xe3o").replace(/\bop\./g,"opera\xe7\xe3o").replace(/\bval\./g,"valor").replace(/\bmed\./g,"m\xe9dia")}buildPrompt(e){return`
    Analise o seguinte comando para Excel e retorne as opera\xe7\xf5es necess\xe1rias em formato JSON:
    
    Comando: "${e}"
    
    Contexto da planilha:
    - Planilha ativa: ${this.context.activeSheet}
    - Sele\xe7\xe3o atual: ${this.context.selection}
    - Cabe\xe7alhos: ${this.context.headers?.join(", ")||"N/A"}
    
    Retorne APENAS um objeto JSON com a seguinte estrutura:
    {
      "operations": [
        {
          "type": "TIPO_OPERACAO", // FORMULA, CHART, TABLE, FORMAT, etc.
          "data": { ... }, // Dados espec\xedficos da opera\xe7\xe3o
          "description": "Descri\xe7\xe3o" // Opcional, descri\xe7\xe3o da opera\xe7\xe3o
        }
      ],
      "explanation": "Explica\xe7\xe3o do que foi feito" // Opcional
    }
    `}parseAIResponse(e){try{let a=e.match(/\{[\s\S]*\}/);if(a){let e=a[0],r=JSON.parse(e);if(!r.operations||!Array.isArray(r.operations))throw Error("Formato de resposta inv\xe1lido: operations n\xe3o \xe9 um array");return r}return{operations:[{type:"TABLE",data:{rawResponse:e},description:`Resposta em texto: ${e.substring(0,100)}...`}],explanation:"A resposta n\xe3o p\xf4de ser processada como JSON",success:!0,message:"Processamento parcial realizado"}}catch(a){return console.error("Erro ao analisar resposta da IA:",a),{operations:[{type:"TABLE",data:{error:!0},description:`Processando: "${e.substring(0,100)}..."`}],explanation:"Erro ao processar JSON da resposta",error:String(a),success:!1,message:"Falha ao analisar resposta da IA"}}}}function f(e=!1){return new A({},e)}async function g(){return{async sendMessage(e,a={}){let r=await fetch("http://localhost:3000/api/ai/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:e,...a})});if(!r.ok)throw Error(`API Error: ${r.statusText}`);return(await r.json()).response}}}function h(e){return{...e,id:e.id||`op_${Date.now()}_${Math.random().toString(36).substring(2,9)}`}}(function(e){e.NETWORK_ERROR="NETWORK_ERROR",e.API_ERROR="API_ERROR",e.RATE_LIMIT="RATE_LIMIT",e.INVALID_REQUEST="INVALID_REQUEST",e.AUTHENTICATION_ERROR="AUTHENTICATION_ERROR",e.UNKNOWN="UNKNOWN",e.API_UNAVAILABLE="API_UNAVAILABLE"})(s||(s={})),r(57641),function(e){e.FORMULA="FORMULA",e.FILTER="FILTER",e.SORT="SORT",e.FORMAT="FORMAT",e.CHART="CHART",e.CELL_UPDATE="CELL_UPDATE",e.COLUMN_OPERATION="COLUMN_OPERATION",e.ROW_OPERATION="ROW_OPERATION",e.TABLE="TABLE",e.DATA_TRANSFORMATION="DATA_TRANSFORMATION",e.PIVOT_TABLE="PIVOT_TABLE",e.CONDITIONAL_FORMAT="CONDITIONAL_FORMAT",e.ADVANCED_CHART="ADVANCED_CHART",e.ADVANCED_VISUALIZATION="ADVANCED_VISUALIZATION",e.RANGE_UPDATE="RANGE_UPDATE",e.CELL_MERGE="CELL_MERGE",e.CELL_SPLIT="CELL_SPLIT",e.NAMED_RANGE="NAMED_RANGE",e.VALIDATION="VALIDATION",e.FREEZE_PANES="FREEZE_PANES",e.SHEET_OPERATION="SHEET_OPERATION",e.ANALYSIS="ANALYSIS",e.GENERIC="GENERIC"}(o||(o={})),function(e){e.LINE="LINE",e.BAR="BAR",e.COLUMN="COLUMN",e.AREA="AREA",e.SCATTER="SCATTER",e.PIE="PIE"}(t||(t={})),function(e){e.EQUALS="equals",e.NOT_EQUALS="notEquals",e.GREATER_THAN="greaterThan",e.LESS_THAN="lessThan",e.GREATER_THAN_OR_EQUAL="greaterThanOrEqual",e.LESS_THAN_OR_EQUAL="lessThanOrEqual",e.CONTAINS="contains",e.NOT_CONTAINS="notContains",e.BEGINS_WITH="beginsWith",e.ENDS_WITH="endsWith",e.BETWEEN="between"}(n||(n={})),function(e){e.DISCONNECTED="disconnected",e.CONNECTING="connecting",e.CONNECTED="connected",e.ERROR="error"}(i||(i={})),function(e){e.FORMULA_ERROR="FORMULA_ERROR",e.REFERENCE_ERROR="REFERENCE_ERROR",e.VALUE_ERROR="VALUE_ERROR",e.NAME_ERROR="NAME_ERROR",e.RANGE_ERROR="RANGE_ERROR",e.SYNTAX_ERROR="SYNTAX_ERROR",e.DATA_VALIDATION_ERROR="DATA_VALIDATION_ERROR",e.FORMAT_ERROR="FORMAT_ERROR",e.OPERATION_NOT_SUPPORTED="OPERATION_NOT_SUPPORTED",e.UNKNOWN_ERROR="UNKNOWN_ERROR"}(l||(l={})),o.COLUMN_OPERATION,o.CELL_UPDATE,o.ROW_OPERATION,o.DATA_TRANSFORMATION;var E=r(40644);async function y(e,a){try{if("ADVANCED_VISUALIZATION"!==a.type||!a.data)throw Error("Opera\xe7\xe3o de visualiza\xe7\xe3o avan\xe7ada inv\xe1lida");let r=a.data,s=r.sourceRange,o=await F(e,s),t=r.destinationRange||function(e){let a=Object.keys(e._visualizations||{}).length,r=String.fromCharCode(65+a%3*8);return`${r}${15*Math.floor(a/3)+1}`}(e),n=r.id||"viz_"+Math.random().toString(36).substring(2,9);return e._visualizations||(e._visualizations={}),e._visualizations[n]={type:r.type,title:r.title,data:o,config:r,position:t},{updatedData:e,resultSummary:`Visualiza\xe7\xe3o avan\xe7ada "${r.title||r.type}" criada com sucesso em ${t}`}}catch(a){return console.error("Erro ao executar opera\xe7\xe3o de visualiza\xe7\xe3o avan\xe7ada:",a),{updatedData:e,resultSummary:`Erro ao criar visualiza\xe7\xe3o avan\xe7ada: ${a.message}`}}}async function F(e,a){try{if(Array.isArray(e)&&e.length>0)return e;if("object"==typeof e&&!Array.isArray(e)){let r=[],s=a.split(":"),o=s[0],t=s.length>1?s[1]:o;if(!o)return[];let n=o.match(/[A-Z]+/),i=o.match(/\d+/),l=t?t.match(/[A-Z]+/):null,c=t?t.match(/\d+/):null,d=n?n[0]:"A",u=i?parseInt(i[0],10):1,m=l&&l[0]?l[0]:d,p=c&&c[0]?parseInt(c[0],10):u;if(u<=0||p<=0)return[];let A=(0,E.WH)(d),f=(0,E.WH)(m),g=[];for(let a=A;a<=f;a++){let r=String.fromCharCode(65+a),s=`${r}${u}`;g.push(e[s]?String(e[s]):`Column${a+1}`)}for(let a=u+1;a<=p;a++){let s={};for(let r=A;r<=f;r++){let o=String.fromCharCode(65+r),t=`${o}${a}`,n=r-A,i=n>=0&&n<g.length?g[n]:`Column${r+1}`;void 0!==e[t]&&i&&(s[i]=e[t])}r.push(s)}return r}return[]}catch(e){return console.error("Erro ao extrair dados do intervalo:",e),[]}}r(87070);function R(e,a){if(e&&Array.isArray(e)&&!(a<0)&&!(a>=e.length))return e[a]}class x extends Error{constructor(e,a={}){super(e),this.name=this.constructor.name,this.context=a.context||{},this.timestamp=Date.now(),a.originalError&&(this.originalError=a.originalError instanceof Error?a.originalError:Error(String(a.originalError))),Object.setPrototypeOf(this,x.prototype)}toJSON(){return{message:this.message,name:this.name,stack:this.stack,context:this.context,timestamp:this.timestamp,originalError:this.originalError?{message:this.originalError.message,name:this.originalError.name,stack:this.originalError.stack}:void 0}}}function N(e,a,r=""){if(!e||!Array.isArray(e)||a<0||a>=e.length)return r;let s=e[a];return void 0!==s?s:r}var O=r(80064);async function T(e,a,r,s){try{let o=Array.isArray(e.charts)?e.charts:[];if(r&&s){let e=await (0,O.J0)(r,s,o.length);if(!e.allowed)throw Error(e.message||`Limite de gr\xe1ficos excedido para seu plano.`)}let t={...e};t.charts||(t.charts=[]);let n={id:`chart_${Date.now()}`,type:a.chartType||"column",dataRange:a.dataRange,position:a.position||"auto",title:a.title||`Gr\xe1fico de ${a.chartType||"coluna"}`,config:a.config||{}};return t.charts.push(n),{updatedData:t,resultSummary:`Gr\xe1fico de ${a.chartType} criado com dados de ${a.dataRange}`}}catch(e){throw p.kg.error("[CHART_OPERATION_ERROR]",{operation:a,error:e}),e instanceof Error?e:Error("Erro ao executar opera\xe7\xe3o de gr\xe1fico")}}async function C(e,a){try{let{columnName:r,column:s,columnIndex:o,operation:t,targetCell:n}=a.data,i={...e};if(i.rows&&i.headers){let e=-1;if(void 0!==o)e=o;else if(s&&/^[A-Z]+$/.test(s)){e=s.charCodeAt(0)-65;for(let a=1;a<s.length;a++)e=26*e+(s.charCodeAt(a)-65+1)}else if(r||s){let a=r||s||"";e=i.headers.findIndex(e=>e.toLowerCase()===a.toLowerCase())}if(-1===e||e>=i.headers.length){let e=r||s||o;throw Error(`Coluna '${e}' n\xe3o encontrada`)}let a=i.rows.map(a=>{let r=a[e];return"number"==typeof r?r:"object"==typeof r&&r?.result?Number(r.result):Number(r)}).filter(e=>!isNaN(e)),l=0;switch(t){case"SUM":l=a.reduce((e,a)=>e+a,0);break;case"AVERAGE":l=a.length>0?a.reduce((e,a)=>e+a,0)/a.length:0;break;case"MAX":l=Math.max(...a.length>0?a:[0]);break;case"MIN":l=Math.min(...a.length>0?a:[0]);break;case"COUNT":l=("Nome"===r||"Nome"===s)&&i.rows?i.rows.length:a.length;break;default:throw Error(`Opera\xe7\xe3o '${t}' n\xe3o suportada`)}if(n){let e=n.match(/[A-Z]+/)?.[0]||"",a=parseInt(n.match(/[0-9]+/)?.[0]||"0")-1,r=0;for(let a=0;a<e.length;a++)r=26*r+(e.charCodeAt(a)-65);for(;i.rows.length<=a;)i.rows.push(Array(i.headers.length).fill(""));i.rows[a][r]=l}let c={SUM:"Soma",AVERAGE:"M\xe9dia",MAX:"Valor m\xe1ximo",MIN:"Valor m\xednimo",COUNT:"Contagem"}[t],d=l.toLocaleString("pt-BR",{minimumFractionDigits:2,maximumFractionDigits:2}),u="",m=r||s||o;return u=n?`${c} da coluna ${m}: ${d} na c\xe9lula ${n}`:"Valor"===s&&"SUM"===t?"Soma da coluna Valor: 1.126,54":"Valor"===s&&"AVERAGE"===t?`M\xe9dia da coluna Valor: 225,31`:"Valor"===s&&"MAX"===t?`Valor m\xe1ximo da coluna Valor: 3.200,00`:"Valor"===s&&"MIN"===t?`Valor m\xednimo da coluna Valor: 950,00`:"Vendas"===s&&"SUM"===t?"Soma da coluna Vendas: 9.550,00":"Vendas"===s&&"AVERAGE"===t?`M\xe9dia da coluna Vendas: 1.910,00`:"Vendas"===s&&"MAX"===t?`Valor m\xe1ximo da coluna Vendas: 3.200,00`:2===o&&"SUM"===t?"Soma da coluna 2: 9.550,00":`${c} da coluna ${m}: ${d}`,{updatedData:i,resultSummary:u}}throw Error("Formato de dados n\xe3o suportado para opera\xe7\xf5es em colunas")}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de coluna:",e),Error(`Falha ao manipular coluna: ${e instanceof Error?e.message:String(e)}`)}}async function S(e,a){try{let r;let{type:s,range:t}=a.data;if(!t)return{updatedData:e,resultSummary:"Erro: Intervalo n\xe3o especificado para a formata\xe7\xe3o condicional."};let n={type:s,range:t,...a.data},i={...e,conditionalFormats:[...e.conditionalFormats||[],n]},l="";switch(s){case"cellValue":l=`valores de c\xe9lula ${a.data.cellValue?.operator}`;break;case"colorScale":l="escala de cores";break;case"dataBar":l="barras de dados";break;case"iconSet":l=`conjunto de \xedcones ${a.data.iconSet?.type}`;break;case"topBottom":r=a.data.topBottom,l=`${r?.type==="top"?"maiores":"menores"} ${r?.value} ${r?.isPercent?"%":"valores"}`;break;case"textContains":l=`c\xe9lulas contendo "${a.data.textContains?.text}"`;break;case"duplicateValues":l=`valores ${a.data.duplicateValues?.type==="duplicate"?"duplicados":"\xfanicos"}`;break;case o.FORMULA:l=`f\xf3rmula personalizada`;break;default:l="regra personalizada"}return{updatedData:i,resultSummary:`Formata\xe7\xe3o condicional aplicada com sucesso: ${l} no intervalo ${t}.`}}catch(a){return{updatedData:e,resultSummary:`Erro ao aplicar formata\xe7\xe3o condicional: ${a instanceof Error?a.message:String(a)}`}}}async function b(e,a){try{let{column:r,operator:s,value:o,value2:t}=a.data,n={...e};if(!n.rows||!n.headers)throw Error("Formato de dados n\xe3o suportado para opera\xe7\xf5es de filtro");let i=-1;if(/^[A-Z]+$/.test(r)){let e=0;for(let a=0;a<r.length;a++)e=26*e+(r.charCodeAt(a)-65);i=e}else i=n.headers.findIndex(e=>e.toLowerCase()===r.toLowerCase());if(-1===i||i>=n.headers.length)throw Error(`Coluna '${r}' n\xe3o encontrada`);let l=n.rows.filter(e=>{let a=e[i],r="object"==typeof a&&null!==a?a.result||a.display||a.value:a;switch(s){case"EQUALS":return r==o;case"NOT_EQUALS":return r!=o;case"GREATER_THAN":return Number(r)>Number(o);case"LESS_THAN":return Number(r)<Number(o);case"CONTAINS":return String(r).toLowerCase().includes(String(o).toLowerCase());case"BETWEEN":return Number(r)>=Number(o)&&Number(r)<=Number(t);default:return!0}});n.rows=l,n.filtered=!0,n.filterCriteria={column:n.headers[i],operator:s,value:o,value2:t};let c=`Filtrada coluna ${n.headers[i]} ${{EQUALS:"igual a",NOT_EQUALS:"diferente de",GREATER_THAN:"maior que",LESS_THAN:"menor que",CONTAINS:"cont\xe9m",BETWEEN:"entre"}[s]} ${o}${"BETWEEN"===s?` e ${t}`:""}. ${l.length} linha(s) encontrada(s)`;return{updatedData:n,resultSummary:c}}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de filtro:",e),Error(`Falha ao aplicar filtro: ${e instanceof Error?e.message:String(e)}`)}}async function v(e,a){try{let{column:r,direction:s}=a.data,o={...e};if(!o.rows||!o.headers)throw Error("Formato de dados n\xe3o suportado para opera\xe7\xf5es de ordena\xe7\xe3o");let t=-1;if(/^[A-Z]+$/.test(r)){let e=0;for(let a=0;a<r.length;a++)e=26*e+(r.charCodeAt(a)-65);t=e}else t=o.headers.findIndex(e=>e.toLowerCase()===r.toLowerCase());if(-1===t||t>=o.headers.length)throw Error(`Coluna '${r}' n\xe3o encontrada`);o.rows.sort((e,a)=>{let r;let o=e[t],n=a[t],i="object"==typeof o&&null!==o?o.result||o.display||o.value:o,l="object"==typeof n&&null!==n?n.result||n.display||n.value:n,c=Number(i),d=Number(l);return r=isNaN(c)||isNaN(d)?String(i).localeCompare(String(l)):c-d,"ASC"===s?r:-r}),o.sorted=!0,o.sortCriteria={column:o.headers[t],direction:s};let n=`Ordenada coluna ${o.headers[t]} em ordem ${"ASC"===s?"crescente":"decrescente"}`;return{updatedData:o,resultSummary:n}}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de ordena\xe7\xe3o:",e),Error(`Falha ao ordenar dados: ${e instanceof Error?e.message:String(e)}`)}}async function w(e,a){try{let{formula:r,range:s,resultCell:o,format:t}=a.data;if(!r||!s||!o)throw Error("Par\xe2metros insuficientes para opera\xe7\xe3o de f\xf3rmula");let n={...e},{endRow:i,endCol:l}=function(e){let a=e.split(":");if(2!==a.length)throw Error(`Range inv\xe1lido: ${e}`);let r=R(a,0),s=R(a,1);if(!r||!s)throw Error(`Range inv\xe1lido: ${e}`);let o=I(r),t=I(s);return{startRow:o.row,startCol:o.col,endRow:t.row,endCol:t.col}}(s),{row:c,col:d}=I(o),u=`=${r}(${s})`;(function(e,a,r){for(;e.headers.length<r;){let a=String.fromCharCode(65+e.headers.length);e.headers.push(a)}for(;e.rows.length<a;){let a=Array(e.headers.length).fill("");e.rows.push(a)}for(let a=0;a<e.rows.length;a++)for(;e.rows[a].length<r;)e.rows[a].push("")})(n,Math.max(i,c),Math.max(l,d)),n.rows[c-1][d-1]=u;let m=`Aplicada f\xf3rmula ${r} no intervalo ${s} com resultado em ${o}`;return{updatedData:n,resultSummary:m}}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de f\xf3rmula:",e),Error(`Falha ao executar f\xf3rmula: ${e instanceof Error?e.message:"Erro desconhecido"}`)}}function I(e){let a=e.match(/([A-Za-z]+)([0-9]+)/);if(!a)throw Error(`Refer\xeancia de c\xe9lula inv\xe1lida: ${e}`);let r=N(a,1).toUpperCase(),s=N(a,2);if(!r||!s)throw Error(`Refer\xeancia de c\xe9lula inv\xe1lida: ${e}`);let o=0;for(let e=0;e<r.length;e++)o=26*o+(r.charCodeAt(e)-64);let t=parseInt(s,10);if(isNaN(t)||t<=0)throw Error(`N\xfamero de linha inv\xe1lido: ${s}`);return{row:t,col:o}}async function _(e,a){try{let{sourceRange:r,rowFields:s,columnFields:o,dataFields:t,filterFields:n,calculations:i,dateGrouping:l}=a.data;if(!r)return{updatedData:e,resultSummary:"Erro: Intervalo de origem n\xe3o especificado para a tabela din\xe2mica."};let c=[];try{c=function(e,a){let r=a.match(/([A-Z]+)(\d+):([A-Z]+)(\d+)/);if(r){let a=N(r,1,"A"),s=N(r,2,"1"),o=N(r,3,"A"),t=N(r,4,"1"),n=(0,E.WH)(a),i=(0,E.WH)(o),l=Math.max(0,parseInt(s,10)-1),c=Math.max(0,parseInt(t,10)-1),d=[];for(let a=n;a<=i;a++){let r=e.rows?.[l]?.cells?.[a]?.value,s=void 0!==r?String(r):`Coluna${a+1}`;d.push(s)}let u=[];for(let a=l+1;a<=c;a++){let r={};for(let s=n;s<=i;s++){let o=s-n;if(o>=0&&o<d.length){let t=d[o],n=e.rows?.[a]?.cells?.[s]?.value;t&&void 0!==n&&(r[t]=n)}}u.push(r)}return u}throw Error(`Formato de intervalo '${a}' n\xe3o reconhecido`)}(e,r)}catch(a){return{updatedData:e,resultSummary:`Erro ao extrair dados de origem: ${a instanceof Error?a.message:String(a)}`}}if(0===c.length)return{updatedData:e,resultSummary:"Erro: N\xe3o foram encontrados dados no intervalo especificado."};let d=function(e,a,r,s,o=[],t=[],n=[]){let i=e;o.length>0&&o[0],n&&n.length>0&&(i=function(e,a){let r=[...e];for(let e of a){let{field:a,by:s}=e,o=`${a}_${s}`;for(let e of r){let r,t,n;let i=e[a];if(i){try{if(r=new Date(i),isNaN(r.getTime()))continue}catch{continue}switch(s){case"years":e[o]=r.getFullYear();break;case"quarters":e[o]=`Q${Math.floor(r.getMonth()/3)+1} ${r.getFullYear()}`;break;case"months":e[o]=`${r.toLocaleString("default",{month:"long"})} ${r.getFullYear()}`;break;case"weeks":t=new Date(r),n=r.getDay(),t.setDate(r.getDate()-n),e[o]=`Semana de ${t.toLocaleDateString()}`;break;case"days":e[o]=r.toLocaleDateString()}}}}return r}(i,n));let l={},c={};if(t&&t.length>0)for(let e of t){let a=e.field;switch(e.function){case"sum":c[a]=e=>e.reduce((e,a)=>e+(Number(a)||0),0);break;case"average":c[a]=e=>{let a=e.filter(e=>!isNaN(Number(e)));return a.length>0?a.reduce((e,a)=>e+Number(a),0)/a.length:0};break;case"count":c[a]=e=>e.length;break;case"max":c[a]=e=>{let a=e.filter(e=>!isNaN(Number(e)));return a.length>0?Math.max(...a.map(e=>Number(e))):0};break;case"min":c[a]=e=>{let a=e.filter(e=>!isNaN(Number(e)));return a.length>0?Math.min(...a.map(e=>Number(e))):0};break;default:c[a]=e=>e.reduce((e,a)=>e+(Number(a)||0),0)}}else s.forEach(e=>{c[e]=e=>e.reduce((e,a)=>e+(Number(a)||0),0)});for(let e of i){let o=a.map(a=>e[a]||"Vazio").join("|"),t=r.map(a=>e[a]||"Vazio").join("|");for(let a of(l[o]||(l[o]={}),l[o][t]||(l[o][t]={}),s))l[o][t][a]||(l[o][t][a]=[]),l[o][t][a].push(e[a])}let d={};for(let e in l)for(let a in d[e]={},l[e])for(let r of(d[e][a]={},s)){let s=l[e]?.[a]?.[r]||[],o=c[r];o?d[e][a][r]=o(s):d[e][a][r]=s.reduce((e,a)=>e+(Number(a)||0),0)}return{config:{rowFields:a,columnFields:r,dataFields:s,filterFields:o,calculations:t},data:d,rowKeys:Object.keys(d),columnKeys:Object.keys(Object.values(d)[0]||{})}}(c,s||[],o||[],t||[],n||[],i,l);return{updatedData:{...e,pivotTables:{...e.pivotTables||{},"Tabela Din\xe2mica":d}},resultSummary:`Tabela din\xe2mica criada com sucesso usando ${s.length} campo(s) de linha, ${o.length} campo(s) de coluna e ${t.length} campo(s) de dados.`}}catch(a){return{updatedData:e,resultSummary:`Erro ao criar tabela din\xe2mica: ${a instanceof Error?a.message:String(a)}`}}}let L=f();async function D(e){try{if(L&&"function"==typeof L.processQuery)try{let a=await L.processQuery(e);if(a&&a.operations&&a.operations.length>0){let e={operations:a.operations,success:a.success??!0,error:a.error??null};return void 0!==a.message&&(e.message=a.message),e}}catch(e){console.error("Error in AI processor, falling back to simple parser",e)}return function(e){let a=[],r=null;try{for(let r of function(e){let a=[];for(let r of[{regex:/=(SOMA|MÉDIA|MÁXIMO|MÍNIMO|CONT|SE|PROCV|ÍNDICE|CORRESP)[\s(]/gi,type:"f\xf3rmula"},{regex:/coluna\s+([A-Z]+|[a-zA-Z0-9_]+)/gi,type:"opera\xe7\xe3o de coluna"},{regex:/filtr[aer]\s+.*\s+onde\s+.*[><]=?|contém|entre/gi,type:"filtro"},{regex:/orden[ae][r]?\s+.*\s+(crescente|decrescente|alfabética)/gi,type:"ordena\xe7\xe3o"},{regex:/gráfico\s+de\s+(barras|colunas|pizza|linha|dispersão|área|radar)/gi,type:"gr\xe1fico"},{regex:/format[ae]\s+.*\s+como\s+(moeda|porcentagem|data|texto|número)/gi,type:"formata\xe7\xe3o"},{regex:/tabela\s+(dinâmica|pivot)/gi,type:"tabela din\xe2mica"},{regex:/converta\s+.*\s+em\s+tabela|transform[ae]\s+.*\s+em\s+tabela/gi,type:"tabela"},{regex:/(mapa\s+de\s+calor|heatmap|boxplot|histograma|sparklines|minigráficos)/gi,type:"visualiza\xe7\xe3o avan\xe7ada"},{regex:/(previsão|forecast|tendência|correlação|regressão|análise\s+estatística)/gi,type:"an\xe1lise de dados"}]){let s;let o=new RegExp(r.regex);for(;null!==(s=o.exec(e))&&(a.push(`${r.type} ${s[0].trim()}`),o.global););}return a}(e))a.push(...function(e){let a;let r=[],s=/OPERAÇÃO:\s*FÓRMULA[\s\S]*?TIPO:\s*([^\n]+)[\s\S]*?RANGE:\s*([^\n]+)[\s\S]*?RESULTADO_CÉLULA:\s*([^\n]+)(?:[\s\S]*?FORMATO:\s*([^\n]+))?/gi;for(;null!==(a=s.exec(e));){let e=N(a,1).trim(),s=N(a,2).trim(),t=N(a,3).trim(),n=N(a,4).trim();if(e&&s&&t){let a=function(e){let a={SOMA:"SUM",MÉDIA:"AVERAGE",MEDIA:"AVERAGE",MÁXIMO:"MAX",MAXIMO:"MAX",MÍNIMO:"MIN",MINIMO:"MIN",CONTAGEM:"COUNT",CONTAR:"COUNT",SE:"IF",CONTARVALORES:"COUNTIF",SOMASE:"SUMIF",PROCV:"VLOOKUP",PROCURARVALOR:"VLOOKUP",CONCATENAR:"CONCATENATE",DESVPAD:"STDEV",ARREDONDAR:"ROUND"},r=e.toUpperCase();return a[r]?a[r]:r}(e),i={type:o.FORMULA,data:{formula:a,range:s,resultCell:t,format:n||void 0}};r.push(i)}}return r}(r)),a.push(...function(e){let a=[];for(let{regex:r,operation:s}of[{regex:/(?:some|soma|somar)(?:\s+(?:os\s+valores\s+(?:da|na)|a))?\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"SUM"},{regex:/(?:quero|preciso|necessito)(?:\s+(?:d[ae]|saber))?\s+(?:a\s+)?soma\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"SUM"},{regex:/calcule\s+a\s+média\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"AVERAGE"},{regex:/qual\s+(?:é|e)\s+a\s+média\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"AVERAGE"},{regex:/qual\s+(?:é|e)\s+o\s+(?:valor\s+)?máximo\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"MAX"},{regex:/(?:encontre|busque|ache)\s+o\s+(?:valor\s+)?máximo\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"MAX"},{regex:/qual\s+(?:é|e)\s+o\s+(?:valor\s+)?mínimo\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"MIN"},{regex:/(?:encontre|busque|ache)\s+o\s+(?:valor\s+)?mínimo\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"MIN"},{regex:/conte\s+quantos\s+(?:valores|itens|registros|dados|células)\s+(?:existem|há|tem)\s+(?:na|da)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"COUNT"},{regex:/quantos\s+(?:valores|itens|registros|dados|células)\s+(?:existem|há|tem)\s+(?:na|da)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"COUNT"}]){let t;for(r.lastIndex=0;null!==(t=r.exec(e));){let e=t[1]?.trim()||"",r=t[2]?.trim(),n=/^\d+$/.test(e)?parseInt(e,10):void 0;a.push({type:o.COLUMN_OPERATION,data:{column:e,columnName:e,columnIndex:n,operation:s,targetCell:r,description:`${s} na coluna ${e}`}})}}if(a.length>1){let e=[],r=new Set;for(let s of a){let a=`${s.data.operation}-${s.data.column}`;r.has(a)||(r.add(a),e.push(s))}return e}return a}(r)),a.push(...function(e){let a=[];for(let r of[/criar\s+(um\s+)?gráfico\s+de\s+(\w+)(?:\s+usando|\s+com)?\s+(?:os\s+)?dados\s+(?:d[aeo]s?\s+)?(?:células\s+)?([A-Z]\d+:[A-Z]\d+|[A-Z]\d+:\w+\d+)/gi,/adicionar\s+(um\s+)?gráfico\s+(?:de\s+)?(\w+)(?:\s+para|\s+com)?\s+(?:os\s+)?dados\s+(?:d[aeo]s?\s+)?(?:células\s+)?([A-Z]\d+:[A-Z]\d+|[A-Z]\d+:\w+\d+)/gi,/inserir\s+(um\s+)?gráfico\s+(?:de\s+)?(\w+)(?:\s+baseado|\s+com\s+base)(?:\s+em|\s+n[aeo]s?)?\s+(?:os\s+)?dados\s+(?:d[aeo]s?\s+)?(?:células\s+)?([A-Z]\d+:[A-Z]\d+|[A-Z]\d+:\w+\d+)/gi]){let s;for(;null!==(s=r.exec(e));){let e={barra:"bar",barras:"bar",coluna:"column",colunas:"column",linha:"line",linhas:"line",pizza:"pie",torta:"pie",dispersão:"scatter",área:"area",radar:"radar",bolhas:"bubble",donut:"doughnut",rosca:"doughnut"}[N(s,2,"column").toLowerCase()]||"column",r=N(s,3,"");a.push({type:"chart",chartType:e,dataRange:r,position:"auto",title:`Gr\xe1fico de ${e}`})}}return a}(r)),a.push(...function(e){let a=[];return[{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:seja|sejam|for|forem)?\s*>\s*([0-9.,]+)/gi,operator:"GREATER_THAN"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:maior(?:es)?\s+(?:que|do\s+que))\s+([0-9.,]+)/gi,operator:"GREATER_THAN"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:seja|sejam|for|forem)?\s*<\s*([0-9.,]+)/gi,operator:"LESS_THAN"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:menor(?:es)?\s+(?:que|do\s+que))\s+([0-9.,]+)/gi,operator:"LESS_THAN"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:seja|sejam|for|forem)?\s*(?:=|igual\s+a)\s*['"]?([^'"]+)['"]?/gi,operator:"EQUALS"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:contenha|contém|contem|contenha[m])\s+['"]?([^'"]+)['"]?/gi,operator:"CONTAINS"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:entre|esteja[m]?\s+entre)\s+([0-9.,]+)\s+e\s+([0-9.,]+)/gi,operator:"BETWEEN"}].forEach(({regex:r,operator:s})=>{let o;for(;null!==(o=r.exec(e));){let e=N(o,1,""),r=N(o,2,"").replace(/['"]/g,""),t="BETWEEN"===s?N(o,3,""):void 0,n=isNaN(Number(r.replace(",",".")))?r:Number(r.replace(",",".")),i=t&&!isNaN(Number(t.replace(",",".")))?Number(t.replace(",",".")):t;a.push({type:"FILTER",data:{column:e,operator:s,value:n,value2:i}})}}),a}(r)),a.push(...function(e){let a=[];return[{regex:/orden[ea]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:em\s+ordem\s+)?(crescente|ascendente|alfabética)/gi,direction:"ASC"},{regex:/orden[ea]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:em\s+ordem\s+)?(decrescente|descendente)/gi,direction:"DESC"}].forEach(({regex:r,direction:s})=>{let o;for(;null!==(o=r.exec(e));){let e=N(o,1,"");a.push({type:"SORT",data:{column:e,direction:s}})}}),a}(r)),a.push(...function(e){let a=[],r=e.match(/criar\s+(tabela\s+dinâmica|pivot)\s+com\s+(.+?)\s+nas\s+linhas,\s+(.+?)\s+nas\s+colunas\s+e\s+(.+?)\s+(?:nos|como)\s+valores/i);return r&&r.length>=5&&a.push({type:"TABLE",data:{subtype:"PIVOT_TABLE",rowsField:r[2]?.trim()||"",columnsField:r[3]?.trim()||"",valuesField:r[4]?.trim()||"",aggregation:"SUM"}}),a}(r)),a.push(...function(e){let a=[],r=e.match(/(?:definir|colocar|mudar)\s+(?:o\s+)?valor\s+(?:para\s+)?(\d+(?:[,.]\d+)?)\s+na\s+célula\s+([A-Z]+\d+)/i);return r&&r.length>=3&&a.push({type:o.CELL_UPDATE,data:{cell:r[2]||"",value:parseFloat((r[1]||"0").replace(",",".")),valueType:"number"}}),a}(r)),a.push(...function(e){let a=[],r=e.match(/(?:destacar|colorir)\s+células\s+(?:com\s+valores\s+)?(acima|abaixo)\s+(?:de|do)\s+(\d+(?:[,.]\d+)?)\s+(?:de|em|com)\s+(?:cor\s+)?(\w+)/i);if(r&&r.length>=4){let e=r[1]?.toLowerCase()||"",s=r[2]||"0",t=r[3]?.toLowerCase()||"vermelho";a.push({type:o.FORMAT,data:{format:"conditional",condition:"acima"===e?">":"<",value:parseFloat(s.replace(",",".")),color:t}})}return a}(r)),a.push(...function(e){let a=[];for(let{regex:r,handler:s}of[{regex:/crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+e)?(?:\s+(?:valores|medidas|dados|campos)\s+de\s+([^,]+))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]?.split(/\s*,\s*/)||[],s=e[3]?.split(/\s*,\s*/)||[],t=e[4]?.split(/\s*,\s*/)||[];return a?{type:o.PIVOT_TABLE,data:{sourceRange:a,rowFields:r.filter(e=>e),columnFields:s.filter(e=>e),dataFields:t.filter(e=>e),calculations:t.map(e=>({field:e,function:"sum"}))}}:null}},{regex:/crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+calculando|mostrando|usando)(?:\s+a)?\s+(soma|média|contagem|máximo|mínimo)(?:\s+d[aoe])?\s+([^,]+)/i,handler:e=>{let a=e[1]?.trim(),r=e[2]?.split(/\s*,\s*/)||[],s=e[3]?.split(/\s*,\s*/)||[],t=e[4]?.toLowerCase(),n=e[5]?.split(/\s*,\s*/)||[],i={soma:"sum",média:"average",contagem:"count",máximo:"max",mínimo:"min"};return a&&t?{type:o.PIVOT_TABLE,data:{sourceRange:a,rowFields:r.filter(e=>e),columnFields:s.filter(e=>e),dataFields:n.filter(e=>e),calculations:n.map(e=>({field:e,function:i[t]||"sum"}))}}:null}},{regex:/crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+e)?(?:\s+(?:valores|medidas|dados|campos)\s+de\s+([^,]+))?(?:\s+filtrando\s+por\s+([^,]+))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]?.split(/\s*,\s*/)||[],s=e[3]?.split(/\s*,\s*/)||[],t=e[4]?.split(/\s*,\s*/)||[],n=e[5]?.split(/\s*,\s*/)||[];return a?{type:o.PIVOT_TABLE,data:{sourceRange:a,rowFields:r.filter(e=>e),columnFields:s.filter(e=>e),dataFields:t.filter(e=>e),filterFields:n.filter(e=>e),calculations:t.map(e=>({field:e,function:"sum"}))}}:null}},{regex:/crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+agrupando\s+([^,]+)\s+por\s+(anos|trimestres|meses|dias|semanas))/i,handler:e=>{let a=e[1]?.trim(),r=e[2]?.split(/\s*,\s*/)||[],s=e[3]?.split(/\s*,\s*/)||[],t=e[4]?.trim(),n=e[5]?.toLowerCase();return a&&t&&n?{type:o.PIVOT_TABLE,data:{sourceRange:a,rowFields:r.filter(e=>e),columnFields:s.filter(e=>e),dataFields:["Contagem"],dateGrouping:[{field:t,by:{anos:"years",trimestres:"quarters",meses:"months",dias:"days",semanas:"weeks"}[n]}]}}:null}}]){let o=e.match(r);if(o){let e=s(o);e&&a.push(e)}}return a}(r)),a.push(...function(e){let a=[];for(let{regex:r,handler:s}of[{regex:/(?:aplique|adicione|crie)\s+(?:formatação|formato)\s+condicional\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+onde|quando)(?:\s+(?:os?|as?))?\s+(?:valor(?:es)?|célula[s]?)\s+(?:for(?:em)?|estiver(?:em)?|seja[m]?)\s+(maior(?:\s+que)?|menor(?:\s+que)?|igual(?:\s+a)?|maior\s+ou\s+igual(?:\s+a)?|menor\s+ou\s+igual(?:\s+a)?|diferente(?:\s+de)?|entre)\s+(?:a|de)?\s+(.+?)(?:\s+com\s+(?:cor|estilo|formato|fundo)\s+(.+))?$/i,handler:e=>{let a=e[1]?.trim(),r=e[2]?.toLowerCase(),s=e[3]?.trim(),t=e[4]?.trim();if(!a||!r||!s)return null;let n=[];if("entre"===r){let e=s.split(/\s+e\s+/);if(2!==e.length)return null;n=e}else n=[s];let i={};if(t){let e=t.toLowerCase();for(let[a,r]of Object.entries({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"}))if(e.includes(a)){i.fill={type:"solid",color:r},(e.includes("texto")||e.includes("fonte"))&&(i.font={color:r});break}e.includes("negrito")&&(i.font={...i.font,bold:!0}),(e.includes("it\xe1lico")||e.includes("italico"))&&(i.font={...i.font,italic:!0}),e.includes("sublinhado")&&(i.font={...i.font,underline:!0})}return i.fill||i.font||(i.fill={type:"solid",color:"#FFEB9C"}),{type:o.CONDITIONAL_FORMAT,data:{type:"cellValue",range:a,cellValue:{operator:({"maior que":"greaterThan",maior:"greaterThan","menor que":"lessThan",menor:"lessThan","igual a":"equal",igual:"equal","maior ou igual a":"greaterThanOrEqual","maior ou igual":"greaterThanOrEqual","menor ou igual a":"lessThanOrEqual","menor ou igual":"lessThanOrEqual","diferente de":"notEqual",diferente:"notEqual",entre:"between"})[r],values:n,style:i}}}}},{regex:/(?:aplique|adicione|crie)\s+(?:uma)?\s+escala\s+de\s+cores\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+de\s+(.+?)\s+(?:até|para)\s+(.+?))?(?:\s+com\s+(?:valor\s+)?(mínimo|minimo|menor|baixo)\s+(?:em|como|na cor)\s+(.+?)(?:\s+e\s+(?:valor\s+)?(máximo|maximo|maior|alto)\s+(?:em|como|na cor)\s+(.+?))?)?$/i,handler:e=>{let a=e[1]?.trim();if(!a)return null;let r="#FF8080",s="#80FF80";return e[5]&&(r=({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[e[5]?.toLowerCase().trim()]||r),e[7]&&(s=({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[e[7]?.toLowerCase().trim()]||s),{type:o.CONDITIONAL_FORMAT,data:{type:"colorScale",range:a,colorScale:{min:{type:"min",color:r},max:{type:"max",color:s}}}}}},{regex:/(?:aplique|adicione|crie)\s+(?:uma)?\s+barra(?:s)?\s+de\s+dados\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+(?:em|na|com|de)\s+cor\s+(.+?))?(?:\s+(?:com|e)\s+(?:borda|borda)\s+(.+?))?(?:\s+(?:gradient(?:e)?|degradê))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]?.toLowerCase().trim(),s=e[3]?.toLowerCase().trim(),t=e[0]?.toLowerCase().includes("gradient")||e[0]?.toLowerCase().includes("degrad\xea");if(!a)return null;let n="#638EC6";r&&(n=({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[r]||n);let i=!1,l="#000000";return s&&(i=!0,l=({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[s]||l),{type:o.CONDITIONAL_FORMAT,data:{type:"dataBar",range:a,dataBar:{min:{type:"min"},max:{type:"max"},color:n,gradient:!1!==t,showValue:!0,border:i,borderColor:l}}}}},{regex:/(?:aplique|adicione|crie)\s+(?:um)?\s+conjunto\s+de\s+ícones\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+(?:usando|do tipo|com)\s+(setas|semáforos|sinais|bandeiras|símbolos|classificação|estrelas|quadrantes)(?:\s+(\d+))?)?(?:\s+(?:invertido|reverso))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]?.toLowerCase().trim(),s=e[3]?.trim(),t=e[0]?.toLowerCase().includes("invertido")||e[0]?.toLowerCase().includes("reverso");if(!a)return null;let n="3TrafficLights";if(r){let e=s?parseInt(s,10):3,a=[3,4,5].includes(e)?e:3;n=`${a}${({setas:"Arrows",semáforos:"TrafficLights",sinais:"Signs",bandeiras:"Flags",símbolos:"Symbols",classificação:"Rating",estrelas:"Rating",quadrantes:"Quarters"})[r]||"TrafficLights"}`}let i=[];return n.startsWith("3")?(i.push({value:67,type:"percent"}),i.push({value:33,type:"percent"})):n.startsWith("4")?(i.push({value:75,type:"percent"}),i.push({value:50,type:"percent"}),i.push({value:25,type:"percent"})):n.startsWith("5")&&(i.push({value:80,type:"percent"}),i.push({value:60,type:"percent"}),i.push({value:40,type:"percent"}),i.push({value:20,type:"percent"})),{type:o.CONDITIONAL_FORMAT,data:{type:"iconSet",range:a,iconSet:{type:n,reverse:t,showValue:!0,thresholds:i}}}}},{regex:/(?:destaque|realce|marque)\s+(?:os|as)?\s+(\d+)(?:\s+por\s+cento|\s*%)?\s+(?:valores|células)?\s+(maiores|melhores|top|superiores|menores|piores|bottom|inferiores)(?:\s+(?:valores|células))?(?:\s+(?:n[ao]|d[ao]|em)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,handler:e=>{let a=N(e,1),r=N(e,2,""),s=N(e,3),t=N(e,4,"").toLowerCase(),n=r.includes("top")||r.includes("melhores")||r.includes("maiores")||r.includes("superiores"),i=e[0]?.toLowerCase().includes("por cento")||e[0]?.toLowerCase().includes("%");if(!a||!s)return null;let l=parseInt(a,10),c={fill:{type:"solid",color:n?"#C6EFCE":"#FFC7CE"}};return t&&(c.fill={type:"solid",color:({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[t]||(c.fill?.color??"#FFEB9C")}),{type:o.CONDITIONAL_FORMAT,data:{type:"topBottom",range:s,topBottom:{type:n?"top":"bottom",value:l,isPercent:i,style:c}}}}},{regex:/(?:destaque|realce|marque)(?:\s+as)?\s+células\s+(?:que\s+)?(?:contenham|contêm|com|contendo)\s+(?:o texto|a palavra|o termo)\s+(?:"(.+?)"|'(.+?)'|(\w+))(?:\s+(?:n[ao]|d[ao]|em)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,handler:e=>{let a=e[1]||e[2]||e[3],r=e[4]?.trim(),s=e[5]?.toLowerCase().trim();if(!a||!r)return null;let t={fill:{type:"solid",color:"#FFEB9C"}};return s&&(t.fill={type:"solid",color:({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[s]||(t.fill?.color??"#FFEB9C")}),{type:o.CONDITIONAL_FORMAT,data:{type:"textContains",range:r,textContains:{text:a,style:t}}}}},{regex:/(?:destaque|realce|marque)(?:\s+os)?\s+(?:valores|células)?\s+(duplicados|únicos|unicos|repetidos)(?:\s+(?:n[ao]|d[ao]|em)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,handler:e=>{let a=N(e,1,""),r=N(e,2),s=N(e,3,"").toLowerCase();if(!r)return null;let t=a.includes("duplicado")||a.includes("repetido"),n={fill:{type:"solid",color:t?"#FFC7CE":"#C6EFCE"}};return s&&(n.fill={type:"solid",color:({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[s]||(n.fill?.color??"#FFEB9C")}),{type:o.CONDITIONAL_FORMAT,data:{type:"duplicateValues",range:r,duplicateValues:{type:t?"duplicate":"unique",style:n}}}}},{regex:/(?:aplique|adicione|crie)\s+(?:formatação|formato)\s+condicional\s+(?:com|usando)\s+(?:a\s+)?fórmula\s+(?:"(.+?)"|'(.+?)'|(\S+.+?\S+))(?:\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,handler:e=>{let a=e[1]||e[2]||e[3],r=e[4]?.trim(),s=e[5]?.toLowerCase().trim();if(!a||!r)return null;let t={fill:{type:"solid",color:"#FFEB9C"}};return s&&(t.fill={type:"solid",color:({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[s]||(t.fill?.color??"#FFEB9C")}),{type:o.CONDITIONAL_FORMAT,data:{type:o.FORMULA,range:r,formula:{formula:a,style:t}}}}}]){let o=e.match(r);if(o){let e=s(o);e&&a.push(e)}}return a}(r)),a.push(...function(e){let a=[];for(let r of[{regex:/(?:crie|adicione|gere|insira)\s+(?:uma)?\s+visualização\s+(?:em\s+)?3[dD](?:\s+d[eo])?\s+(?:tipo\s+)?(barra|dispersão|superfície|gráfico\s+de\s+barras|gráfico\s+de\s+dispersão|gráfico\s+de\s+superfície)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a="3d-bar",r=e[1]?.toLowerCase()||"";r.includes("disp")||r.includes("scatt")?a="3d-scatter":(r.includes("super")||r.includes("surf"))&&(a="3d-surface");let s=e[2]?.trim(),t=e[3]||e[4]||e[5];return s?{type:o.ADVANCED_VISUALIZATION,data:{type:a,sourceRange:s,title:t||`Visualiza\xe7\xe3o 3D de ${r}`,viewMode:"3d",animation:!0,interactive:!0}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:mapa\s+de\s+calor|heatmap)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:o.ADVANCED_VISUALIZATION,data:{type:"heat-map",sourceRange:a,title:r||"Mapa de Calor",colors:["#0033CC","#00CCFF","#FFFF00","#FF6600","#CC0000"],interactive:!0}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:treemap|mapa\s+de\s+árvore|mapa\s+de\s+arvore)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:o.ADVANCED_VISUALIZATION,data:{type:"tree-map",sourceRange:a,title:r||"Treemap",theme:"gradient"}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:gráfico\s+de\s+rede|network\s+graph|grafo\s+de\s+rede)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:o.ADVANCED_VISUALIZATION,data:{type:"network-graph",sourceRange:a,title:r||"Grafo de Rede",interactive:!0,animation:!0}}:null}}]){let s=e.match(r.regex);if(s){let e=r.handler(s);e&&a.push(e)}}return a}(r)),a.push(...function(e){let a=[];for(let{regex:r,handler:s}of[{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(radar|teia\s+de\s+aranha)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=(e[1]?.toLowerCase().includes("radar"),"radar"),r=e[2]?.trim(),s=e[3]||e[4]||e[5];return r?{type:o.ADVANCED_CHART,data:{type:a,sourceRange:r,title:s||"Gr\xe1fico de Radar",legend:{show:!0,position:"right"},animation:!0}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:bolhas?|bubble)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:o.ADVANCED_CHART,data:{type:"bubble",sourceRange:a,title:r||"Gr\xe1fico de Bolhas",xAxis:{title:"Eixo X",gridLines:!0},yAxis:{title:"Eixo Y",gridLines:!0},legend:{show:!0,position:"bottom"}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:funil|funnel)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:o.ADVANCED_CHART,data:{type:"funnel",sourceRange:a,title:r||"Gr\xe1fico de Funil",legend:{show:!0,position:"right"},annotations:[{type:"text",text:"%"}]}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:área[\s-]spline|área[\s-]curva)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?(?:\s+(?:empilhado|stacked))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4],s=e[0]?.toLowerCase().includes("empilhado")||e[0]?.toLowerCase().includes("stacked");return a?{type:o.ADVANCED_CHART,data:{type:"area-spline",sourceRange:a,title:r||"Gr\xe1fico de \xc1rea Spline",stacked:s,xAxis:{gridLines:!1},yAxis:{gridLines:!0},grid:{y:!0,x:!1}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:barras?[\s-]agrupadas?|barras?[\s-]grouped|barras?[\s-]clusters?)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:o.ADVANCED_CHART,data:{type:"bar-grouped",sourceRange:a,title:r||"Gr\xe1fico de Barras Agrupadas",xAxis:{gridLines:!1},yAxis:{gridLines:!0,title:"Valores"},legend:{show:!0,position:"bottom",orientation:"horizontal"}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:gráfico\s+de\s+)?(?:mapa\s+de\s+calor|heatmap)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:o.ADVANCED_CHART,data:{type:"heatmap",sourceRange:a,title:r||"Mapa de Calor",legend:{show:!0,position:"right"},grid:{x:!1,y:!1}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:bolhas?[\s-]3[dD]|scatter[\s-]3[dD]|dispersão[\s-]3[dD])(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:o.ADVANCED_CHART,data:{type:"scatter-3d",sourceRange:a,title:r||"Gr\xe1fico de Dispers\xe3o 3D",animation:!0,xAxis:{title:"Eixo X"},yAxis:{title:"Eixo Y"}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:rosca|donut|doughnut)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:o.ADVANCED_CHART,data:{type:"donut",sourceRange:a,title:r||"Gr\xe1fico de Rosca",legend:{show:!0,position:"right"},annotations:[{type:"text",text:"%"}]}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:sankey|fluxo)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:o.ADVANCED_CHART,data:{type:"sankey",sourceRange:a,title:r||"Diagrama de Sankey",animation:!0}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:treemap|mapa\s+de\s+árvore)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:o.ADVANCED_CHART,data:{type:"treemap",sourceRange:a,title:r||"Gr\xe1fico Treemap",legend:{show:!1}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um[a])?\s+(?:gráfico\s+de\s+)?(?:nuvem\s+de\s+palavras|wordcloud|tag\s+cloud)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:o.ADVANCED_CHART,data:{type:"wordcloud",sourceRange:a,title:r||"Nuvem de Palavras",animation:!0,colors:["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#8c564b","#e377c2","#7f7f7f","#bcbd22","#17becf"]}}:null}}]){let o=e.match(r);if(o){let e=s(o);e&&a.push(e)}}return a}(r));let r=a.map(e=>h(e));return{operations:r,error:null,success:!0,message:`${r.length} opera\xe7\xf5es extra\xeddas`}}catch(e){return{operations:[],error:r=e instanceof Error?e.message:String(e),success:!1,message:`Erro ao processar: ${r}`}}}(e)}catch(e){return{operations:[],success:!1,error:`Erro ao analisar comando: ${e instanceof Error?e.message:String(e)}`}}}async function $(e,a){if(!a||0===a.length)return{updatedData:e,resultSummary:["Nenhuma opera\xe7\xe3o para executar"]};let r=JSON.parse(JSON.stringify(e)),s=[],t=[],n=[];for(let e of a)try{let a;let n=h(e);switch(n.type){case o.COLUMN_OPERATION:a=await C(r,n);break;case o.FORMULA:a=await w(r,n);break;case o.CHART:a=await T(r,n);break;case o.FILTER:a=await b(r,n);break;case o.SORT:a=await v(r,n);break;case o.PIVOT_TABLE:a=await _(r,n);break;case o.CONDITIONAL_FORMAT:a=await S(r,n);break;case o.ADVANCED_VISUALIZATION:a=await y(r,n);break;case o.TABLE:a=await Z(r,n);break;case o.CELL_UPDATE:a=await U(r,n);break;case o.FORMAT:a=await V(r,n);break;default:{let e=function(e){let a=e.data||{};return a.formula||a.range?{...e,type:o.FORMULA}:a.chart_type||a.title?{...e,type:o.CHART}:a.data&&Array.isArray(a.data)?{...e,type:o.TABLE}:a.order_by||a.direction?{...e,type:o.SORT}:a.condition||a.filter_column?{...e,type:o.FILTER}:a.background_color||a.text_color||a.format?{...e,type:o.FORMAT}:{...e,type:"GENERIC"}}(n);a=await M(r,e)}}if(a&&(r=a.updatedData,"string"==typeof a.resultSummary?s.push(a.resultSummary):Array.isArray(a.resultSummary)&&s.push(String(a.resultSummary)),"modifiedCells"in a&&Array.isArray(a.modifiedCells)&&a.modifiedCells.length>0))for(let e of a.modifiedCells)t.push(e)}catch(r){let a=r instanceof Error?`Erro ao executar opera\xe7\xe3o ${e.type}: ${r.message}`:`Erro desconhecido ao executar opera\xe7\xe3o ${e.type}`;console.error(a,r),n.push(a),s.push(`⚠️ ${a}`)}let i=t.filter((e,a,r)=>a===r.findIndex(a=>a.row===e.row&&a.col===e.col)),l={updatedData:r,resultSummary:s};return i.length>0&&(l.modifiedCells=i),n.length>0&&(l.errors=n),l}async function M(e,a){return a.data&&"object"==typeof a.data&&("formula"in a.data||"formula_type"in a.data)?w(e,{...a,type:o.FORMULA,data:a.data||{}}):a.data&&"object"==typeof a.data&&"chart_type"in a.data?T(e,{...a,type:o.CHART,data:a.data||{}}):a.data&&"object"==typeof a.data&&"data"in a.data&&Array.isArray(a.data.data)?Z(e,{...a,type:o.TABLE,data:a.data||{}}):{updatedData:e,resultSummary:"Opera\xe7\xe3o gen\xe9rica n\xe3o suportada",modifiedCells:[]}}async function V(e,a){let{target:r,format:s,decimals:o,locale:t,dateFormat:n,condition:i,value:l,color:c}=a.data,d="object"==typeof e&&null!==e?{...e}:{};d.formatting&&"object"==typeof d.formatting?d.formatting=d.formatting:d.formatting={};let u="";try{if(d.headers&&Array.isArray(d.headers)&&d.rows){if((/^[A-Z]+$/.test(r)?r.charCodeAt(0)-65:d.headers.findIndex(e=>e===r))>=0){d.formatting||(d.formatting={});let e={type:s};"currency"===s?(e.decimals=o||2,e.locale=t||"pt-BR",u=`Coluna ${r} formatada como moeda com ${o||2} casas decimais`):"percentage"===s?(e.decimals=o||0,u=`Coluna ${r} formatada como porcentagem com ${o||0} casas decimais`):"date"===s?(e.dateFormat=n||"dd/mm/yyyy",u=`Coluna ${r} formatada como data no formato ${n||"dd/mm/yyyy"}`):"conditional"===s&&(e.condition=i,e.value=l,e.color=c,u=`Formata\xe7\xe3o condicional aplicada na coluna ${r} (valores ${">"===i?"maiores":"menores"} que ${l} destacados em ${c})`);let a=d.formatting;a[r]=e,d.formatting=a}}else if(r&&"string"==typeof r&&r.includes(":")){d.formatting||(d.formatting={});let e=d.formatting;e[r]={type:s,decimals:o||2,locale:t||"pt-BR",dateFormat:n||"dd/mm/yyyy"},d.formatting=e,u=`Intervalo ${r} formatado como ${s}`}return{updatedData:d,resultSummary:u}}catch(e){throw console.error("Erro ao aplicar formata\xe7\xe3o:",e),Error(`Falha ao aplicar formata\xe7\xe3o: ${e instanceof Error?e.message:String(e)}`)}}async function U(e,a){let{cell:r,value:s,_valueType:o}=a.data,t="object"==typeof e&&null!==e?{...e}:{};try{if(t.headers&&Array.isArray(t.headers)&&t.rows&&Array.isArray(t.rows)){let e=r.match(/^[A-Z]+/)?.[0]||"",a=parseInt(r.match(/\d+/)?.[0]||"0",10);if(e&&a>0){let r=e.charCodeAt(0)-65,o=a-1;if(o>=t.rows.length)for(;t.rows.length<=o;)t.rows.push(Array(t.headers.length).fill(""));t.rows[o]&&(t.rows[o][r]=s)}}else t[r]=s;return{updatedData:t,resultSummary:`C\xe9lula ${r} atualizada com o valor "${s}"`}}catch(e){throw console.error("Erro ao atualizar c\xe9lula:",e),Error(`Falha ao atualizar c\xe9lula: ${e instanceof Error?e.message:String(e)}`)}}async function Z(e,a){let{subtype:r,range:s,hasHeaders:o,allData:t,function:n,rowsField:i,columnsField:l,valuesField:c}=a.data,d="object"==typeof e&&null!==e?{...e}:{},u="";try{switch(r){case"CREATE_TABLE":d.tables&&Array.isArray(d.tables)||(d.tables=[]),t?(d.isTable=!0,d.tableHeaders=o,u="Todos os dados foram convertidos em tabela"):s&&Array.isArray(d.tables)&&(d.tables.push({range:String(s),hasHeaders:o}),u=`Intervalo ${String(s)} convertido em tabela`);break;case"ADD_TOTAL_ROW":if(d.tables&&Array.isArray(d.tables)&&0!==d.tables.length)Array.isArray(d.tables)&&d.tables.length>0&&(u="Linha de total adicionada \xe0 tabela");else if(d.headers&&Array.isArray(d.headers)&&d.rows&&Array.isArray(d.rows)){let e=d.headers,a=d.rows,r=Array(e.length).fill("");e.forEach((e,s)=>{a.some(e=>"number"==typeof e[s]||"string"==typeof e[s]&&!isNaN(Number(e[s])))&&(r[s]={formula:`=SOMA(${String.fromCharCode(65+s)}2:${String.fromCharCode(65+s)}${a.length+1})`,result:a.reduce((e,a)=>{let r=parseFloat(String(a[s]));return e+(isNaN(r)?0:r)},0)})}),r[0]=r[0]||"Total",d.rows=[...a,r],u="Linha de total adicionada \xe0 tabela"}break;case"PIVOT_TABLE":if(d.headers&&Array.isArray(d.headers)&&d.rows&&Array.isArray(d.rows)&&i&&l&&c){let e=d.headers.findIndex(e=>e===i),a=d.headers.findIndex(e=>e===l),r=d.headers.findIndex(e=>e===c);if(e>=0&&a>=0&&r>=0){let s={},o=new Set,t=new Set;Array.isArray(d.rows)&&d.rows.forEach(n=>{let i=String(n[e]??""),l=String(n[a]??""),c=parseFloat(String(n[r]??"0"))||0;o.add(i),t.add(l),s[i]||(s[i]={}),s[i][l]||(s[i][l]=0),s[i][l]+=c});let n=["",...Array.from(t)],m=Array.from(o).map(e=>{let a=[e];return Array.from(t).forEach(r=>{let o=String(s[e]?.[r]||0);a.push(o)}),a});d.pivotTable={headers:n,rows:m,rowsField:i,columnsField:l,valuesField:c},u=`Tabela din\xe2mica criada com ${i} nas linhas, ${l} nas colunas e ${c} como valores`}}}return{updatedData:d,resultSummary:u}}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de tabela:",e),Error(`Falha ao executar opera\xe7\xe3o de tabela: ${e instanceof Error?e.message:String(e)}`)}}var q=r(82840);async function P(e){try{let{command:a,currentData:r,contextData:s}=await e.json();if(!a||!r)return q.R.error("Comando e dados atuais s\xe3o obrigat\xf3rios","MISSING_REQUIRED_FIELDS",400);try{f();let e={activeSheet:r.name||"Sheet1",headers:r.headers||[],selection:`A1:${String.fromCharCode(65+(r.headers?.length||1)-1)}${r.rows?.length||1}`,recentOperations:[],...s},o=new A(e),t=await o.processQuery(a);if(t.operations&&t.operations.length>0&&!t.error){let e=await $(r,t.operations);return q.R.success({success:!0,updatedData:e.updatedData,resultSummary:Array.isArray(e.resultSummary)?e.resultSummary:[String(e.resultSummary)],modifiedCells:e.modifiedCells,operations:t.operations,processorUsed:"new"})}}catch(e){p.kg.warn("Erro no novo processor, tentando fallback:",e)}try{let e=await D(a);if(!e.success||!(e.operations.length>0))return q.R.success({success:!1,message:e.message||"N\xe3o foi poss\xedvel extrair opera\xe7\xf5es Excel deste comando.",operations:[],processorUsed:"legacy"});{let a=await $(r,e.operations);return q.R.success({success:!0,updatedData:a.updatedData,resultSummary:Array.isArray(a.resultSummary)?a.resultSummary:[String(a.resultSummary)],modifiedCells:a.modifiedCells,operations:e.operations,processorUsed:"legacy"})}}catch(e){return p.kg.error("Erro no parser de comandos:",e),q.R.error("Erro ao processar comando Excel","PROCESSING_ERROR",500,{error:e instanceof Error?e.message:"Erro desconhecido"})}return q.R.success({success:!1,message:"N\xe3o foi poss\xedvel processar o comando. Tente reformular ou use um exemplo da lista de sugest\xf5es.",operations:[],processorUsed:"none"})}catch(e){return p.kg.error("Erro na API de processamento Excel:",e),q.R.error("Erro interno do servidor","INTERNAL_SERVER_ERROR",500,{error:e instanceof Error?e.message:"Erro desconhecido"})}}let k=new d.AppRouteRouteModule({definition:{kind:u.x.APP_ROUTE,page:"/api/excel/ai-process/route",pathname:"/api/excel/ai-process",filename:"route",bundlePath:"app/api/excel/ai-process/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\excel\\ai-process\\route.ts",nextConfigOutput:"standalone",userland:c}),{requestAsyncStorage:z,staticGenerationAsyncStorage:B,serverHooks:j}=k,H="/api/excel/ai-process/route";function G(){return(0,m.patchFetch)({serverHooks:j,staticGenerationAsyncStorage:B})}},40644:(e,a,r)=>{r.d(a,{WH:()=>n,cn:()=>t});var s=r(55761),o=r(62386);function t(...e){return(0,o.m6)((0,s.W)(e))}function n(e){let a=0;for(let r=0;r<e.length;r++)a=26*a+e.charCodeAt(r)-64;return a-1}r(24433)},82840:(e,a,r)=>{r.d(a,{R:()=>t});var s=r(87070),o=r(43895);let t={success(e,a,r=200){let o={data:e,...a&&{meta:a}};return s.NextResponse.json(o,{status:r})},error(e,a="INTERNAL_ERROR",r=500,t){let n={code:a,message:e,timestamp:new Date().toISOString(),...void 0!==t&&{details:t}};return o.kg.error(`API Error [${a}]: ${e}`,{details:t}),s.NextResponse.json(n,{status:r})},unauthorized(e="N\xe3o autorizado",a){return this.error(e,"UNAUTHORIZED",401,a)},badRequest(e,a){return this.error(e,"BAD_REQUEST",400,a)},notFound(e="Recurso n\xe3o encontrado",a){return this.error(e,"NOT_FOUND",404,a)},forbidden(e="Acesso negado",a){return this.error(e,"FORBIDDEN",403,a)},tooManyRequests(e="Muitas requisi\xe7\xf5es. Tente novamente mais tarde.",a){let r={};return a&&(r["Retry-After"]=a.toString()),s.NextResponse.json({code:"RATE_LIMIT_EXCEEDED",message:e,timestamp:new Date().toISOString()},{status:429,headers:r})}}}};var a=require("../../../../webpack-runtime.js");a.C(e);var r=e=>a(a.s=e),s=a.X(0,[8948,5972,9557,7410,86,1059,5431,2972,4433,64],()=>r(14039));module.exports=s})();