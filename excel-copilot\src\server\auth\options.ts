import { PrismaAdapter } from '@auth/prisma-adapter';
import type { Session, User, Account, Profile } from 'next-auth';
import type { JWT } from 'next-auth/jwt';
import CredentialsProvider from 'next-auth/providers/credentials';
import GithubProvider from 'next-auth/providers/github';
import GoogleProvider from 'next-auth/providers/google';

import { logger } from '@/lib/logger';
import { SessionUser } from '@/types/next-auth';

import { ENV } from '../../config/unified-environment';
import { prisma } from '../db/client';

/**
 * Opções de configuração para o Next Auth
 * Configuradas para funcionar com a versão 4.24.11
 */
export const authOptions = {
  adapter: PrismaAdapter(prisma),
  session: {
    strategy: 'jwt' as const,
    maxAge: 24 * 60 * 60, // 24 horas (reduzido de 30 dias para maior segurança)
    updateAge: 60 * 60, // Atualizar sessão a cada 1 hora
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/signin?error=AuthError',
  },
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax' as const,
        path: '/',
        secure: ENV.IS_PRODUCTION,
      },
    },
    callbackUrl: {
      name: `next-auth.callback-url`,
      options: {
        sameSite: 'lax' as const,
        path: '/',
        secure: ENV.IS_PRODUCTION,
      },
    },
    csrfToken: {
      name: `next-auth.csrf-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax' as const,
        path: '/',
        secure: ENV.IS_PRODUCTION,
      },
    },
  },
  debug: ENV.IS_DEVELOPMENT, // Debug apenas em desenvolvimento
  logger: {
    error(code: string, ...message: unknown[]) {
      logger.error('AUTH ERROR', { code, message });
    },
    warn(code: string, ...message: unknown[]) {
      logger.warn('AUTH WARNING', { code, message });
    },
    debug(code: string, ...message: unknown[]) {
      if (ENV.IS_DEVELOPMENT) {
        logger.debug('AUTH DEBUG', { code, message });
      }
    },
  },
  providers: ENV.IS_PRODUCTION
    ? [
        // Em produção, sempre usar provedores OAuth
        GoogleProvider({
          clientId: ENV.API_KEYS.GOOGLE_CLIENT_ID,
          clientSecret: ENV.API_KEYS.GOOGLE_CLIENT_SECRET,
          allowDangerousEmailAccountLinking: false, // Desabilitado para maior segurança
          authorization: {
            params: {
              scope: 'openid email profile',
              prompt: 'consent',
              access_type: 'offline',
              response_type: 'code',
            },
          },
        }),
        GithubProvider({
          clientId: ENV.API_KEYS.GITHUB_CLIENT_ID,
          clientSecret: ENV.API_KEYS.GITHUB_CLIENT_SECRET,
          allowDangerousEmailAccountLinking: false, // Desabilitado para maior segurança
        }),
      ]
    : ENV.FEATURES.SKIP_AUTH_PROVIDERS || process.env.AUTH_SKIP_PROVIDERS === 'true'
      ? [
          // Em desenvolvimento com SKIP_AUTH_PROVIDERS, usar provedor simples
          CredentialsProvider({
            name: 'Desenvolvimento',
            credentials: {
              email: { label: 'Email', type: 'text' },
              password: { label: 'Password', type: 'password' },
            },
            async authorize() {
              // Para fins de desenvolvimento, permitimos login sem credenciais
              return {
                id: 'dev-user',
                name: 'Usuário Desenvolvimento',
                email: '<EMAIL>',
              };
            },
          }),
        ]
      : [
          // Em desenvolvimento sem SKIP_AUTH_PROVIDERS, usar OAuth também
          GoogleProvider({
            clientId: ENV.API_KEYS.GOOGLE_CLIENT_ID,
            clientSecret: ENV.API_KEYS.GOOGLE_CLIENT_SECRET,
            allowDangerousEmailAccountLinking: true,
            authorization: {
              params: {
                scope: 'openid email profile',
                prompt: 'consent',
                access_type: 'offline',
                response_type: 'code',
              },
            },
          }),
          GithubProvider({
            clientId: ENV.API_KEYS.GITHUB_CLIENT_ID,
            clientSecret: ENV.API_KEYS.GITHUB_CLIENT_SECRET,
            allowDangerousEmailAccountLinking: true,
          }),
        ],
  callbacks: {
    /**
     * Callback de login - permite ou bloqueia o login
     */
    async signIn({
      user,
      account,
      profile: _profile,
    }: {
      user: User;
      account: Account | null;
      profile?: Profile;
    }) {
      try {
        // Log detalhado do processo de login para segurança
        logger.info('🔐 Tentativa de login', {
          userId: user?.id,
          email: user?.email ? user.email.replace(/(.{2}).*(@.*)/, '$1***$2') : 'N/A', // Mascarar email
          provider: account?.provider,
          type: account?.type,
          timestamp: new Date().toISOString(),
          userAgent: 'N/A', // TODO: Capturar user agent do request
        });

        // Verificar se o usuário está banido
        if (user?.id) {
          const dbUser = await prisma.user.findUnique({
            where: { id: user.id },
            select: { isBanned: true, banReason: true, banDate: true },
          });

          if (dbUser?.isBanned) {
            logger.warn('🚫 Tentativa de login de usuário banido', {
              userId: user.id,
              email: user.email ? user.email.replace(/(.{2}).*(@.*)/, '$1***$2') : 'N/A',
              banReason: dbUser.banReason,
              banDate: dbUser.banDate,
            });
            return false;
          }
        }

        // Permitir login para todos os provedores OAuth configurados
        if (account?.type === 'oauth') {
          logger.info('✅ Login OAuth autorizado', {
            provider: account.provider,
            email: user?.email ? user.email.replace(/(.{2}).*(@.*)/, '$1***$2') : 'N/A',
            userId: user?.id,
            timestamp: new Date().toISOString(),
          });
          return true;
        }

        // Permitir login para credenciais (se configurado)
        if (account?.type === 'credentials') {
          logger.info('✅ Login por credenciais autorizado', {
            email: user?.email ? user.email.replace(/(.{2}).*(@.*)/, '$1***$2') : 'N/A',
            userId: user?.id,
            timestamp: new Date().toISOString(),
          });
          return true;
        }

        // Log de tentativa de login não autorizada
        logger.warn('❌ Tentativa de login não autorizada', {
          accountType: account?.type,
          provider: account?.provider,
          email: user?.email ? user.email.replace(/(.{2}).*(@.*)/, '$1***$2') : 'N/A',
          timestamp: new Date().toISOString(),
          reason: 'Tipo de conta não suportado',
        });

        return true; // Permitir por padrão para evitar bloqueios
      } catch (error) {
        logger.error('💥 Erro no callback signIn', {
          error: error instanceof Error ? error.message : 'Erro desconhecido',
          stack: error instanceof Error ? error.stack : undefined,
          userId: user?.id,
          email: user?.email ? user.email.replace(/(.{2}).*(@.*)/, '$1***$2') : 'N/A',
          provider: account?.provider,
          timestamp: new Date().toISOString(),
        });

        // Em caso de erro, permitir o login para evitar bloqueios
        return true;
      }
    },

    /**
     * Callback de sessão - adiciona ID do usuário à sessão e verifica segurança
     */
    async session({ session, token }: { session: Session; token: JWT }) {
      try {
        if (session.user && token.sub) {
          const sessionUser = session.user as SessionUser;
          sessionUser.id = token.sub;

          // Verificar se o usuário ainda existe e não está banido
          const dbUser = await prisma.user.findUnique({
            where: { id: token.sub },
            select: {
              id: true,
              isBanned: true,
              email: true,
              banReason: true,
              banDate: true
            },
          });

          if (!dbUser) {
            logger.warn('🚫 Sessão para usuário inexistente', {
              userId: token.sub,
              email: session.user.email ? session.user.email.replace(/(.{2}).*(@.*)/, '$1***$2') : 'N/A',
              timestamp: new Date().toISOString(),
            });
            // Retornar sessão vazia para forçar novo login
            return { ...session, user: null } as any;
          }

          if (dbUser.isBanned) {
            logger.warn('🚫 Sessão para usuário banido', {
              userId: token.sub,
              email: session.user.email ? session.user.email.replace(/(.{2}).*(@.*)/, '$1***$2') : 'N/A',
              banReason: dbUser.banReason,
              banDate: dbUser.banDate,
              timestamp: new Date().toISOString(),
            });
            // Retornar sessão vazia para forçar novo login
            return { ...session, user: null } as any;
          }

          // Adicionar informações de segurança à sessão
          sessionUser.isBanned = dbUser.isBanned;

          // Log de sessão ativa (apenas em desenvolvimento)
          if (ENV.NODE_ENV === 'development') {
            logger.info('🔐 Sessão ativa verificada', {
              userId: token.sub,
              email: session.user.email ? session.user.email.replace(/(.{2}).*(@.*)/, '$1***$2') : 'N/A',
              timestamp: new Date().toISOString(),
            });
          }
        }

        return session;
      } catch (error) {
        logger.error('💥 Erro no callback de sessão', {
          error: error instanceof Error ? error.message : 'Erro desconhecido',
          userId: token?.sub,
          email: session?.user?.email ? session.user.email.replace(/(.{2}).*(@.*)/, '$1***$2') : 'N/A',
          timestamp: new Date().toISOString(),
        });

        // Em caso de erro, retornar sessão original para não quebrar a aplicação
        return session;
      }
    },

    /**
     * Callback JWT - preserva ID do usuário no token
     */
    async jwt({ token, user }: { token: JWT; user?: User }) {
      if (user?.id) {
        token.sub = user.id;
      }
      return token;
    },

    /**
     * Callback de redirecionamento - garante URLs seguras
     */
    async redirect({ url, baseUrl }: { url: string; baseUrl: string }) {
      // URLs permitidas para redirecionamento
      const allowedPaths = ['/dashboard', '/workbook', '/account', '/pricing', '/'];

      try {
        // URLs relativas
        if (url.startsWith('/')) {
          const path = url.split('?')[0] || '/';
          if (allowedPaths.some(allowed => path.startsWith(allowed))) {
            return `${baseUrl}${url}`;
          }
          return `${baseUrl}/dashboard`;
        }

        // URLs absolutas do mesmo domínio
        if (url.startsWith(baseUrl)) {
          const relativePath = url.replace(baseUrl, '');
          const path = relativePath.split('?')[0] || '/';
          if (allowedPaths.some(allowed => path.startsWith(allowed))) {
            return url;
          }
        }

        // Fallback seguro
        return `${baseUrl}/dashboard`;
      } catch {
        return `${baseUrl}/dashboard`;
      }
    },
  },
  secret: ENV.NEXTAUTH_SECRET,
  // Configurações adicionais para produção
  trustHost: true, // Necessário para Vercel
  useSecureCookies: ENV.IS_PRODUCTION, // Usar cookies seguros em produção
};
