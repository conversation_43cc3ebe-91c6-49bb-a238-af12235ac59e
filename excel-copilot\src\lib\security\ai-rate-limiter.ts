/**
 * Rate Limiter Específico para APIs de IA
 * Implementa limites granulares baseados em tipo de usuário, operação e contexto
 */

import { ENV } from '@/config/unified-environment';
import { logger } from '@/lib/logger';
import { prisma } from '@/server/db/client';

export interface AIRateLimitConfig {
  // Limites por tipo de usuário
  freeUserLimits: {
    requestsPerMinute: number;
    requestsPerHour: number;
    requestsPerDay: number;
    maxTokensPerRequest: number;
  };
  proUserLimits: {
    requestsPerMinute: number;
    requestsPerHour: number;
    requestsPerDay: number;
    maxTokensPerRequest: number;
  };
  // Limites por tipo de operação
  operationLimits: {
    chat: { weight: number; cooldown: number };
    analysis: { weight: number; cooldown: number };
    generation: { weight: number; cooldown: number };
    translation: { weight: number; cooldown: number };
  };
  // Configurações de bloqueio
  blockDuration: {
    soft: number; // Bloqueio temporário
    hard: number; // Bloqueio por abuso
  };
}

export interface AIRateLimitResult {
  allowed: boolean;
  remaining: {
    minute: number;
    hour: number;
    day: number;
  };
  resetTime: {
    minute: number;
    hour: number;
    day: number;
  };
  reason?: string;
  retryAfter?: number;
}

export interface AIUsageMetrics {
  userId: string;
  requestCount: {
    minute: number;
    hour: number;
    day: number;
  };
  tokenUsage: {
    input: number;
    output: number;
    total: number;
  };
  operationCounts: Record<string, number>;
  lastRequest: Date;
  isBlocked: boolean;
  blockReason?: string;
  blockUntil?: Date;
}

/**
 * Sistema de Rate Limiting Avançado para IA
 */
export class AIRateLimiter {
  private static instance: AIRateLimiter;
  private config: AIRateLimitConfig;
  private usageCache = new Map<string, AIUsageMetrics>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutos

  private constructor() {
    this.config = {
      freeUserLimits: {
        requestsPerMinute: ENV.IS_PRODUCTION ? 10 : 50,
        requestsPerHour: ENV.IS_PRODUCTION ? 100 : 500,
        requestsPerDay: ENV.IS_PRODUCTION ? 500 : 2000,
        maxTokensPerRequest: 4000,
      },
      proUserLimits: {
        requestsPerMinute: ENV.IS_PRODUCTION ? 30 : 100,
        requestsPerHour: ENV.IS_PRODUCTION ? 500 : 1000,
        requestsPerDay: ENV.IS_PRODUCTION ? 2000 : 5000,
        maxTokensPerRequest: 8000,
      },
      operationLimits: {
        chat: { weight: 1, cooldown: 1000 }, // 1 segundo
        analysis: { weight: 2, cooldown: 2000 }, // 2 segundos
        generation: { weight: 3, cooldown: 3000 }, // 3 segundos
        translation: { weight: 1.5, cooldown: 1500 }, // 1.5 segundos
      },
      blockDuration: {
        soft: 5 * 60 * 1000, // 5 minutos
        hard: 60 * 60 * 1000, // 1 hora
      },
    };
  }

  public static getInstance(): AIRateLimiter {
    if (!AIRateLimiter.instance) {
      AIRateLimiter.instance = new AIRateLimiter();
    }
    return AIRateLimiter.instance;
  }

  /**
   * Verifica se uma requisição de IA é permitida
   */
  public async checkLimit(
    userId: string,
    operationType: keyof AIRateLimitConfig['operationLimits'] = 'chat',
    estimatedTokens: number = 1000,
    userTier: 'free' | 'pro' = 'free'
  ): Promise<AIRateLimitResult> {
    try {
      // Obter métricas de uso atuais
      const metrics = await this.getUserMetrics(userId);
      
      // Verificar se usuário está bloqueado
      if (metrics.isBlocked && metrics.blockUntil && metrics.blockUntil > new Date()) {
        return {
          allowed: false,
          remaining: { minute: 0, hour: 0, day: 0 },
          resetTime: { minute: 0, hour: 0, day: 0 },
          reason: `Usuário bloqueado: ${metrics.blockReason}`,
          retryAfter: Math.ceil((metrics.blockUntil.getTime() - Date.now()) / 1000),
        };
      }

      // Obter limites baseados no tier do usuário
      const limits = userTier === 'pro' ? this.config.proUserLimits : this.config.freeUserLimits;
      
      // Verificar limite de tokens por requisição
      if (estimatedTokens > limits.maxTokensPerRequest) {
        return {
          allowed: false,
          remaining: { minute: 0, hour: 0, day: 0 },
          resetTime: { minute: 0, hour: 0, day: 0 },
          reason: `Requisição excede limite de tokens (${estimatedTokens}/${limits.maxTokensPerRequest})`,
        };
      }

      // Calcular peso da operação
      const operationWeight = this.config.operationLimits[operationType]?.weight || 1;
      const weightedRequest = operationWeight;

      // Verificar limites temporais
      const now = new Date();
      const minuteStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours(), now.getMinutes());
      const hourStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours());
      const dayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      // Calcular remaining e reset times
      const remaining = {
        minute: Math.max(0, limits.requestsPerMinute - metrics.requestCount.minute),
        hour: Math.max(0, limits.requestsPerHour - metrics.requestCount.hour),
        day: Math.max(0, limits.requestsPerDay - metrics.requestCount.day),
      };

      const resetTime = {
        minute: minuteStart.getTime() + 60 * 1000,
        hour: hourStart.getTime() + 60 * 60 * 1000,
        day: dayStart.getTime() + 24 * 60 * 60 * 1000,
      };

      // Verificar se algum limite foi excedido
      if (metrics.requestCount.minute + weightedRequest > limits.requestsPerMinute) {
        await this.recordViolation(userId, 'minute_limit_exceeded', operationType);
        return {
          allowed: false,
          remaining,
          resetTime,
          reason: 'Limite por minuto excedido',
          retryAfter: Math.ceil((resetTime.minute - now.getTime()) / 1000),
        };
      }

      if (metrics.requestCount.hour + weightedRequest > limits.requestsPerHour) {
        await this.recordViolation(userId, 'hour_limit_exceeded', operationType);
        return {
          allowed: false,
          remaining,
          resetTime,
          reason: 'Limite por hora excedido',
          retryAfter: Math.ceil((resetTime.hour - now.getTime()) / 1000),
        };
      }

      if (metrics.requestCount.day + weightedRequest > limits.requestsPerDay) {
        await this.recordViolation(userId, 'day_limit_exceeded', operationType);
        return {
          allowed: false,
          remaining,
          resetTime,
          reason: 'Limite diário excedido',
          retryAfter: Math.ceil((resetTime.day - now.getTime()) / 1000),
        };
      }

      // Verificar cooldown da operação
      const operationCooldown = this.config.operationLimits[operationType]?.cooldown || 0;
      const timeSinceLastRequest = now.getTime() - metrics.lastRequest.getTime();
      
      if (timeSinceLastRequest < operationCooldown) {
        return {
          allowed: false,
          remaining,
          resetTime,
          reason: 'Cooldown da operação ativo',
          retryAfter: Math.ceil((operationCooldown - timeSinceLastRequest) / 1000),
        };
      }

      // Requisição permitida
      return {
        allowed: true,
        remaining: {
          minute: remaining.minute - weightedRequest,
          hour: remaining.hour - weightedRequest,
          day: remaining.day - weightedRequest,
        },
        resetTime,
      };

    } catch (error) {
      logger.error('Erro ao verificar rate limit de IA:', error);
      // Em caso de erro, permitir a requisição mas registrar o problema
      return {
        allowed: true,
        remaining: { minute: 0, hour: 0, day: 0 },
        resetTime: { minute: 0, hour: 0, day: 0 },
        reason: 'Erro interno - permitindo requisição',
      };
    }
  }

  /**
   * Registra o uso de uma requisição de IA
   */
  public async recordUsage(
    userId: string,
    operationType: keyof AIRateLimitConfig['operationLimits'],
    tokensUsed: { input: number; output: number }
  ): Promise<void> {
    try {
      const metrics = await this.getUserMetrics(userId);

      // Atualizar contadores
      metrics.requestCount.minute++;
      metrics.requestCount.hour++;
      metrics.requestCount.day++;

      // Atualizar uso de tokens
      metrics.tokenUsage.input += tokensUsed.input;
      metrics.tokenUsage.output += tokensUsed.output;
      metrics.tokenUsage.total += tokensUsed.input + tokensUsed.output;

      // Atualizar contadores de operação
      metrics.operationCounts[operationType] = (metrics.operationCounts[operationType] || 0) + 1;

      // Atualizar timestamp
      metrics.lastRequest = new Date();

      // Salvar no cache e banco
      this.usageCache.set(userId, metrics);
      await this.persistMetrics(userId, metrics);

    } catch (error) {
      logger.error('Erro ao registrar uso de IA:', error);
    }
  }

  /**
   * Obtém métricas de uso do usuário
   */
  private async getUserMetrics(userId: string): Promise<AIUsageMetrics> {
    // Verificar cache primeiro
    const cached = this.usageCache.get(userId);
    if (cached) {
      return cached;
    }

    try {
      // Buscar do banco de dados
      const now = new Date();
      const minuteStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours(), now.getMinutes());
      const hourStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours());
      const dayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      // Buscar registros de uso recentes (temporariamente usando valores padrão)
      const [minuteUsage, hourUsage, dayUsage] = [0, 0, 0];
      // TODO: Implementar quando schema do banco for atualizado
      // const [minuteUsage, hourUsage, dayUsage] = await Promise.all([
      //   prisma.aiMetrics.count({
      //     where: {
      //       userId,
      //       createdAt: { gte: minuteStart },
      //     },
      //   }),
      // ]);

      // Buscar último registro para verificar bloqueio (temporariamente usando valores padrão)
      const lastRecord = null;
      const tokenUsage = { _sum: { inputTokens: 0, outputTokens: 0, totalTokens: 0 } };

      // TODO: Implementar quando schema do banco for atualizado
      // const lastRecord = await prisma.aiMetrics.findFirst({
      //   where: { userId },
      //   orderBy: { createdAt: 'desc' },
      // });

      const metrics: AIUsageMetrics = {
        userId,
        requestCount: {
          minute: minuteUsage,
          hour: hourUsage,
          day: dayUsage,
        },
        tokenUsage: {
          input: tokenUsage._sum?.inputTokens || 0,
          output: tokenUsage._sum?.outputTokens || 0,
          total: tokenUsage._sum?.totalTokens || 0,
        },
        operationCounts: {},
        lastRequest: new Date(0),
        isBlocked: false,
      };

      // Cache por 5 minutos
      this.usageCache.set(userId, metrics);
      setTimeout(() => this.usageCache.delete(userId), this.CACHE_TTL);

      return metrics;

    } catch (error) {
      logger.error('Erro ao obter métricas de usuário:', error);
      // Retornar métricas vazias em caso de erro
      return {
        userId,
        requestCount: { minute: 0, hour: 0, day: 0 },
        tokenUsage: { input: 0, output: 0, total: 0 },
        operationCounts: {},
        lastRequest: new Date(0),
        isBlocked: false,
      };
    }
  }

  /**
   * Persiste métricas no banco de dados
   * Temporariamente comentado devido a incompatibilidade de schema
   */
  private async persistMetrics(userId: string, metrics: AIUsageMetrics): Promise<void> {
    try {
      // TODO: Implementar quando schema do banco for atualizado
      // await prisma.aiMetrics.create({
      //   data: {
      //     // Campos serão mapeados quando schema for atualizado
      //   },
      // });
      logger.debug('Métricas persistidas (temporariamente desabilitado)');
    } catch (error) {
      logger.error('Erro ao persistir métricas:', error);
    }
  }

  /**
   * Registra violação de rate limit
   */
  private async recordViolation(userId: string, reason: string, operationType: string): Promise<void> {
    try {
      logger.warn(`Rate limit violation: ${userId} - ${reason} - ${operationType}`);

      // Registrar no banco para auditoria (temporariamente comentado)
      // TODO: Implementar quando schema do banco for atualizado
      // await prisma.securityLog.create({
      //   data: {
      //     userId,
      //     action: 'ai_rate_limit_violation',
      //     details: JSON.stringify({
      //       reason,
      //       operationType,
      //       timestamp: new Date().toISOString(),
      //     }),
      //     ipAddress: 'unknown',
      //     userAgent: 'unknown',
      //   },
      // });

      // Verificar se deve bloquear o usuário por abuso (temporariamente usando valor padrão)
      const recentViolations = 0;
      // TODO: Implementar quando schema do banco for atualizado

      // Bloquear temporariamente se muitas violações
      if (recentViolations >= 5) {
        const metrics = this.usageCache.get(userId);
        if (metrics) {
          metrics.isBlocked = true;
          metrics.blockReason = 'Múltiplas violações de rate limit';
          metrics.blockUntil = new Date(Date.now() + this.config.blockDuration.soft);
          this.usageCache.set(userId, metrics);
        }
      }

    } catch (error) {
      logger.error('Erro ao registrar violação:', error);
    }
  }

  /**
   * Obtém estatísticas de uso de IA
   */
  public async getUsageStats(userId: string): Promise<AIUsageMetrics> {
    return this.getUserMetrics(userId);
  }

  /**
   * Limpa cache de usuário específico
   */
  public clearUserCache(userId: string): void {
    this.usageCache.delete(userId);
  }

  /**
   * Limpa todo o cache
   */
  public clearCache(): void {
    this.usageCache.clear();
  }
}
