/**
 * Serviço de versionamento para arquivos Excel
 * Implementa controle de versões com diff, merge e histórico
 */

import { logger } from '@/lib/logger';
import { backupService } from './backup-service';
import { compressionService } from './compression-service';

export interface VersionInfo {
  id: string;
  workbookId: string;
  userId: string;
  version: string;
  timestamp: string;
  description: string;
  author: string;
  parentVersion: string | undefined;
  changes: ChangeSet[];
  size: number;
  compressed: boolean;
}

export interface ChangeSet {
  type: 'cell' | 'sheet' | 'structure' | 'formula' | 'format';
  action: 'add' | 'update' | 'delete';
  location: string; // Referência da célula, nome da sheet, etc.
  oldValue?: unknown;
  newValue?: unknown;
  timestamp: string;
}

export interface VersionDiff {
  added: ChangeSet[];
  modified: ChangeSet[];
  deleted: ChangeSet[];
  summary: {
    totalChanges: number;
    cellChanges: number;
    sheetChanges: number;
    structureChanges: number;
  };
}

export interface MergeResult {
  success: boolean;
  mergedData: unknown;
  conflicts: MergeConflict[];
  version: string;
}

export interface MergeConflict {
  location: string;
  type: string;
  baseValue: unknown;
  version1Value: unknown;
  version2Value: unknown;
  resolution?: 'version1' | 'version2' | 'manual';
}

export class ExcelVersionService {
  private readonly maxVersionHistory = 50;

  /**
   * Cria uma nova versão do workbook
   */
  async createVersion(
    workbookId: string,
    userId: string,
    data: unknown,
    description: string,
    author: string,
    parentVersion?: string
  ): Promise<VersionInfo> {
    try {
      const version = this.generateVersionNumber();
      const timestamp = new Date().toISOString();

      // Calcular mudanças se há versão pai
      let changes: ChangeSet[] = [];
      if (parentVersion) {
        const parentData = await this.getVersionData(workbookId, userId, parentVersion);
        changes = await this.calculateChanges(parentData, data);
      }

      // Comprimir dados se necessário
      const compressionResult = await compressionService.compressData(
        JSON.stringify(data),
        compressionService.analyzeCompressionStrategy(JSON.stringify(data))
      );

      // Criar backup da versão
      const backupResult = await backupService.createBackup({
        workbookId,
        userId,
        data: compressionResult.compressed ? {
          compressed: true,
          format: compressionResult.format,
          data: Array.from(compressionResult.data),
        } : data,
        version,
        description: `Versão ${version}: ${description}`,
        autoCleanup: false, // Não limpar versões automaticamente
      });

      if (!backupResult.success) {
        throw new Error(`Erro ao criar backup da versão: ${backupResult.error}`);
      }

      const versionInfo: VersionInfo = {
        id: backupResult.backupId,
        workbookId,
        userId,
        version,
        timestamp,
        description,
        author,
        parentVersion: parentVersion || undefined,
        changes,
        size: backupResult.size,
        compressed: compressionResult.compressed,
      };

      // Salvar metadata da versão
      await this.saveVersionMetadata(versionInfo);

      // Limpar versões antigas se necessário
      await this.cleanupOldVersions(workbookId, userId);

      logger.info('Nova versão criada', {
        workbookId,
        userId,
        version,
        changesCount: changes.length,
        size: backupResult.size,
        compressed: compressionResult.compressed,
      });

      return versionInfo;
    } catch (error) {
      logger.error('Erro ao criar versão', {
        workbookId,
        userId,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
      throw error;
    }
  }

  /**
   * Lista versões disponíveis para um workbook
   */
  async listVersions(workbookId: string, userId: string): Promise<VersionInfo[]> {
    try {
      const backups = await backupService.listBackups(workbookId, userId);
      const versions: VersionInfo[] = [];

      for (const backup of backups) {
        try {
          const metadata = await this.getVersionMetadata(backup.id);
          if (metadata) {
            versions.push(metadata);
          }
        } catch (error) {
          logger.warn('Erro ao carregar metadata da versão', {
            backupId: backup.id,
            error: error instanceof Error ? error.message : 'Erro desconhecido',
          });
        }
      }

      return versions.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    } catch (error) {
      logger.error('Erro ao listar versões', {
        workbookId,
        userId,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
      return [];
    }
  }

  /**
   * Obtém dados de uma versão específica
   */
  async getVersionData(workbookId: string, userId: string, version: string): Promise<unknown> {
    try {
      const versions = await this.listVersions(workbookId, userId);
      const versionInfo = versions.find(v => v.version === version);

      if (!versionInfo) {
        throw new Error(`Versão ${version} não encontrada`);
      }

      const data = await backupService.restoreBackup(versionInfo.id, workbookId, userId);

      // Descomprimir se necessário
      if (versionInfo.compressed && typeof data === 'object' && data !== null) {
        const compressedData = data as { compressed: boolean; format: string; data: number[] };
        if (compressedData.compressed) {
          const buffer = Buffer.from(compressedData.data);
          const decompressed = await compressionService.decompressData(buffer, compressedData.format as any);
          return JSON.parse(decompressed.toString());
        }
      }

      return data;
    } catch (error) {
      logger.error('Erro ao obter dados da versão', {
        workbookId,
        userId,
        version,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
      throw error;
    }
  }

  /**
   * Calcula diferenças entre duas versões
   */
  async calculateDiff(
    workbookId: string,
    userId: string,
    version1: string,
    version2: string
  ): Promise<VersionDiff> {
    try {
      const [data1, data2] = await Promise.all([
        this.getVersionData(workbookId, userId, version1),
        this.getVersionData(workbookId, userId, version2),
      ]);

      const changes = await this.calculateChanges(data1, data2);

      const added = changes.filter(c => c.action === 'add');
      const modified = changes.filter(c => c.action === 'update');
      const deleted = changes.filter(c => c.action === 'delete');

      return {
        added,
        modified,
        deleted,
        summary: {
          totalChanges: changes.length,
          cellChanges: changes.filter(c => c.type === 'cell').length,
          sheetChanges: changes.filter(c => c.type === 'sheet').length,
          structureChanges: changes.filter(c => c.type === 'structure').length,
        },
      };
    } catch (error) {
      logger.error('Erro ao calcular diff', {
        workbookId,
        userId,
        version1,
        version2,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
      throw error;
    }
  }

  /**
   * Faz merge de duas versões
   */
  async mergeVersions(
    workbookId: string,
    userId: string,
    baseVersion: string,
    version1: string,
    version2: string,
    conflictResolutions?: Record<string, 'version1' | 'version2' | unknown>
  ): Promise<MergeResult> {
    try {
      const [baseData, data1, data2] = await Promise.all([
        this.getVersionData(workbookId, userId, baseVersion),
        this.getVersionData(workbookId, userId, version1),
        this.getVersionData(workbookId, userId, version2),
      ]);

      // Detectar conflitos
      const conflicts = await this.detectConflicts(baseData, data1, data2);

      // Aplicar resoluções de conflito se fornecidas
      const resolvedConflicts = conflicts.map(conflict => {
        const resolution = conflictResolutions?.[conflict.location];
        if (resolution) {
          return {
            ...conflict,
            resolution: typeof resolution === 'string' ? resolution as any : 'manual',
          };
        }
        return conflict;
      });

      // Se há conflitos não resolvidos, retornar para resolução manual
      const unresolvedConflicts = resolvedConflicts.filter(c => !c.resolution);
      if (unresolvedConflicts.length > 0) {
        return {
          success: false,
          mergedData: null,
          conflicts: unresolvedConflicts,
          version: '',
        };
      }

      // Executar merge
      const mergedData = await this.performMerge(baseData, data1, data2, resolvedConflicts);

      // Criar nova versão com o resultado do merge
      const newVersion = await this.createVersion(
        workbookId,
        userId,
        mergedData,
        `Merge de ${version1} e ${version2}`,
        'Sistema de Merge',
        baseVersion
      );

      return {
        success: true,
        mergedData,
        conflicts: [],
        version: newVersion.version,
      };
    } catch (error) {
      logger.error('Erro no merge de versões', {
        workbookId,
        userId,
        baseVersion,
        version1,
        version2,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
      throw error;
    }
  }

  /**
   * Calcula mudanças entre dois conjuntos de dados
   */
  private async calculateChanges(oldData: unknown, newData: unknown): Promise<ChangeSet[]> {
    const changes: ChangeSet[] = [];
    const timestamp = new Date().toISOString();

    // Implementação simplificada - comparação básica
    // Em uma implementação real, seria necessário um algoritmo mais sofisticado
    try {
      const oldStr = JSON.stringify(oldData, null, 2);
      const newStr = JSON.stringify(newData, null, 2);

      if (oldStr !== newStr) {
        changes.push({
          type: 'structure',
          action: 'update',
          location: 'workbook',
          oldValue: oldData,
          newValue: newData,
          timestamp,
        });
      }
    } catch (error) {
      logger.warn('Erro ao calcular mudanças detalhadas', {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    }

    return changes;
  }

  /**
   * Detecta conflitos entre versões
   */
  private async detectConflicts(
    baseData: unknown,
    data1: unknown,
    data2: unknown
  ): Promise<MergeConflict[]> {
    const conflicts: MergeConflict[] = [];

    // Implementação simplificada
    // Em uma implementação real, seria necessário análise detalhada das estruturas
    try {
      const base = JSON.stringify(baseData);
      const v1 = JSON.stringify(data1);
      const v2 = JSON.stringify(data2);

      if (v1 !== base && v2 !== base && v1 !== v2) {
        conflicts.push({
          location: 'workbook',
          type: 'structure',
          baseValue: baseData,
          version1Value: data1,
          version2Value: data2,
        });
      }
    } catch (error) {
      logger.warn('Erro ao detectar conflitos', {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    }

    return conflicts;
  }

  /**
   * Executa o merge dos dados
   */
  private async performMerge(
    baseData: unknown,
    data1: unknown,
    data2: unknown,
    resolvedConflicts: MergeConflict[]
  ): Promise<unknown> {
    // Implementação simplificada - em caso real seria mais complexa
    for (const conflict of resolvedConflicts) {
      if (conflict.resolution === 'version1') {
        return data1;
      } else if (conflict.resolution === 'version2') {
        return data2;
      }
    }

    // Fallback para versão 1 se não há conflitos
    return data1;
  }

  /**
   * Gera número de versão único
   */
  private generateVersionNumber(): string {
    const now = new Date();
    const timestamp = now.getTime();
    const random = Math.floor(Math.random() * 1000);
    return `${timestamp}.${random}`;
  }

  /**
   * Salva metadata da versão (implementação placeholder)
   */
  private async saveVersionMetadata(versionInfo: VersionInfo): Promise<void> {
    // Em uma implementação real, salvaria no banco de dados
    logger.debug('Metadata da versão salva', { version: versionInfo.version });
  }

  /**
   * Obtém metadata da versão (implementação placeholder)
   */
  private async getVersionMetadata(versionId: string): Promise<VersionInfo | null> {
    // Em uma implementação real, buscaria no banco de dados
    return null;
  }

  /**
   * Remove versões antigas mantendo apenas as mais recentes
   */
  private async cleanupOldVersions(workbookId: string, userId: string): Promise<void> {
    try {
      const versions = await this.listVersions(workbookId, userId);
      
      if (versions.length > this.maxVersionHistory) {
        const versionsToRemove = versions.slice(this.maxVersionHistory);
        
        for (const version of versionsToRemove) {
          try {
            // Remover backup da versão
            // Em implementação real, removeria também metadata do banco
            logger.info('Versão antiga removida', {
              workbookId,
              userId,
              version: version.version,
            });
          } catch (error) {
            logger.warn('Erro ao remover versão antiga', {
              version: version.version,
              error: error instanceof Error ? error.message : 'Erro desconhecido',
            });
          }
        }
      }
    } catch (error) {
      logger.error('Erro na limpeza de versões antigas', {
        workbookId,
        userId,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    }
  }
}

// Instância singleton do serviço de versionamento
export const versionService = new ExcelVersionService();
