/**
 * Serviço de compressão para arquivos Excel
 * Implementa compressão inteligente baseada no tamanho e tipo de dados
 */

import { logger } from '@/lib/logger';

export interface CompressionOptions {
  level?: number; // 1-9, onde 9 é máxima compressão
  threshold?: number; // Tamanho mínimo em bytes para aplicar compressão
  format?: 'gzip' | 'deflate' | 'brotli';
  chunkSize?: number; // Tamanho do chunk para processamento
}

export interface CompressionResult {
  compressed: boolean;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  data: Buffer | Uint8Array;
  format?: string;
  processingTime: number;
}

export class ExcelCompressionService {
  private readonly defaultOptions: Required<CompressionOptions> = {
    level: 6,
    threshold: 1024 * 100, // 100KB
    format: 'gzip',
    chunkSize: 1024 * 64, // 64KB chunks
  };

  /**
   * Comprime dados Excel se necessário
   */
  async compressData(
    data: Buffer | Uint8Array | string,
    options: CompressionOptions = {}
  ): Promise<CompressionResult> {
    const startTime = performance.now();
    const opts = { ...this.defaultOptions, ...options };

    // Converter para Buffer se necessário
    const inputBuffer = this.toBuffer(data);
    const originalSize = inputBuffer.length;

    // Verificar se deve comprimir baseado no threshold
    if (originalSize < opts.threshold) {
      return {
        compressed: false,
        originalSize,
        compressedSize: originalSize,
        compressionRatio: 1,
        data: inputBuffer,
        processingTime: performance.now() - startTime,
      };
    }

    try {
      let compressedData: Buffer;

      // Aplicar compressão baseada no formato escolhido
      switch (opts.format) {
        case 'gzip':
          compressedData = await this.compressGzip(inputBuffer, opts.level);
          break;
        case 'deflate':
          compressedData = await this.compressDeflate(inputBuffer, opts.level);
          break;
        case 'brotli':
          compressedData = await this.compressBrotli(inputBuffer, opts.level);
          break;
        default:
          throw new Error(`Formato de compressão não suportado: ${opts.format}`);
      }

      const compressedSize = compressedData.length;
      const compressionRatio = originalSize / compressedSize;
      const processingTime = performance.now() - startTime;

      // Log da compressão
      logger.info('Compressão aplicada', {
        originalSize,
        compressedSize,
        compressionRatio: compressionRatio.toFixed(2),
        format: opts.format,
        level: opts.level,
        processingTime: Math.round(processingTime),
      });

      return {
        compressed: true,
        originalSize,
        compressedSize,
        compressionRatio,
        data: compressedData,
        format: opts.format,
        processingTime,
      };
    } catch (error) {
      logger.error('Erro na compressão', {
        originalSize,
        format: opts.format,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });

      // Retornar dados originais em caso de erro
      return {
        compressed: false,
        originalSize,
        compressedSize: originalSize,
        compressionRatio: 1,
        data: inputBuffer,
        processingTime: performance.now() - startTime,
      };
    }
  }

  /**
   * Descomprime dados Excel
   */
  async decompressData(
    data: Buffer | Uint8Array,
    format: 'gzip' | 'deflate' | 'brotli'
  ): Promise<Buffer> {
    const inputBuffer = this.toBuffer(data);

    try {
      switch (format) {
        case 'gzip':
          return await this.decompressGzip(inputBuffer);
        case 'deflate':
          return await this.decompressDeflate(inputBuffer);
        case 'brotli':
          return await this.decompressBrotli(inputBuffer);
        default:
          throw new Error(`Formato de descompressão não suportado: ${format}`);
      }
    } catch (error) {
      logger.error('Erro na descompressão', {
        format,
        dataSize: inputBuffer.length,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
      throw error;
    }
  }

  /**
   * Analisa dados Excel e sugere melhor estratégia de compressão
   */
  analyzeCompressionStrategy(data: Buffer | Uint8Array | string): CompressionOptions {
    const buffer = this.toBuffer(data);
    const size = buffer.length;

    // Analisar conteúdo para determinar melhor estratégia
    const sample = buffer.slice(0, Math.min(1024, size));
    const entropy = this.calculateEntropy(sample);

    // Estratégias baseadas no tamanho e entropia
    if (size < 1024 * 50) { // < 50KB
      return { level: 1, format: 'deflate' }; // Compressão rápida
    } else if (size < 1024 * 500) { // < 500KB
      return { level: entropy > 0.8 ? 3 : 6, format: 'gzip' }; // Balanceado
    } else if (size < 1024 * 1024 * 5) { // < 5MB
      return { level: entropy > 0.8 ? 6 : 9, format: 'gzip' }; // Mais compressão
    } else { // > 5MB
      return { level: 9, format: 'brotli' }; // Máxima compressão
    }
  }

  /**
   * Comprime usando GZIP
   */
  private async compressGzip(data: Buffer, level: number): Promise<Buffer> {
    // Implementação usando CompressionStream se disponível (browsers modernos)
    if (typeof CompressionStream !== 'undefined') {
      const stream = new CompressionStream('gzip');
      const writer = stream.writable.getWriter();
      const reader = stream.readable.getReader();

      writer.write(data);
      writer.close();

      const chunks: Uint8Array[] = [];
      let done = false;

      while (!done) {
        const { value, done: readerDone } = await reader.read();
        done = readerDone;
        if (value) {
          chunks.push(value);
        }
      }

      return Buffer.concat(chunks.map(chunk => Buffer.from(chunk)));
    }

    // Fallback para Node.js
    if (typeof require !== 'undefined') {
      const zlib = require('zlib');
      return new Promise((resolve, reject) => {
        zlib.gzip(data, { level }, (err: Error | null, result: Buffer) => {
          if (err) reject(err);
          else resolve(result);
        });
      });
    }

    throw new Error('Compressão GZIP não disponível neste ambiente');
  }

  /**
   * Descomprime GZIP
   */
  private async decompressGzip(data: Buffer): Promise<Buffer> {
    // Implementação usando DecompressionStream se disponível
    if (typeof DecompressionStream !== 'undefined') {
      const stream = new DecompressionStream('gzip');
      const writer = stream.writable.getWriter();
      const reader = stream.readable.getReader();

      writer.write(data);
      writer.close();

      const chunks: Uint8Array[] = [];
      let done = false;

      while (!done) {
        const { value, done: readerDone } = await reader.read();
        done = readerDone;
        if (value) {
          chunks.push(value);
        }
      }

      return Buffer.concat(chunks.map(chunk => Buffer.from(chunk)));
    }

    // Fallback para Node.js
    if (typeof require !== 'undefined') {
      const zlib = require('zlib');
      return new Promise((resolve, reject) => {
        zlib.gunzip(data, (err: Error | null, result: Buffer) => {
          if (err) reject(err);
          else resolve(result);
        });
      });
    }

    throw new Error('Descompressão GZIP não disponível neste ambiente');
  }

  /**
   * Comprime usando Deflate
   */
  private async compressDeflate(data: Buffer, level: number): Promise<Buffer> {
    if (typeof CompressionStream !== 'undefined') {
      const stream = new CompressionStream('deflate');
      const writer = stream.writable.getWriter();
      const reader = stream.readable.getReader();

      writer.write(data);
      writer.close();

      const chunks: Uint8Array[] = [];
      let done = false;

      while (!done) {
        const { value, done: readerDone } = await reader.read();
        done = readerDone;
        if (value) {
          chunks.push(value);
        }
      }

      return Buffer.concat(chunks.map(chunk => Buffer.from(chunk)));
    }

    if (typeof require !== 'undefined') {
      const zlib = require('zlib');
      return new Promise((resolve, reject) => {
        zlib.deflate(data, { level }, (err: Error | null, result: Buffer) => {
          if (err) reject(err);
          else resolve(result);
        });
      });
    }

    throw new Error('Compressão Deflate não disponível neste ambiente');
  }

  /**
   * Descomprime Deflate
   */
  private async decompressDeflate(data: Buffer): Promise<Buffer> {
    if (typeof DecompressionStream !== 'undefined') {
      const stream = new DecompressionStream('deflate');
      const writer = stream.writable.getWriter();
      const reader = stream.readable.getReader();

      writer.write(data);
      writer.close();

      const chunks: Uint8Array[] = [];
      let done = false;

      while (!done) {
        const { value, done: readerDone } = await reader.read();
        done = readerDone;
        if (value) {
          chunks.push(value);
        }
      }

      return Buffer.concat(chunks.map(chunk => Buffer.from(chunk)));
    }

    if (typeof require !== 'undefined') {
      const zlib = require('zlib');
      return new Promise((resolve, reject) => {
        zlib.inflate(data, (err: Error | null, result: Buffer) => {
          if (err) reject(err);
          else resolve(result);
        });
      });
    }

    throw new Error('Descompressão Deflate não disponível neste ambiente');
  }

  /**
   * Comprime usando Brotli (placeholder - implementação simplificada)
   */
  private async compressBrotli(data: Buffer, level: number): Promise<Buffer> {
    // Brotli não está amplamente disponível em browsers
    // Fallback para GZIP
    return this.compressGzip(data, level);
  }

  /**
   * Descomprime Brotli (placeholder)
   */
  private async decompressBrotli(data: Buffer): Promise<Buffer> {
    // Fallback para GZIP
    return this.decompressGzip(data);
  }

  /**
   * Converte dados para Buffer
   */
  private toBuffer(data: Buffer | Uint8Array | string): Buffer {
    if (Buffer.isBuffer(data)) {
      return data;
    }
    if (data instanceof Uint8Array) {
      return Buffer.from(data);
    }
    if (typeof data === 'string') {
      return Buffer.from(data, 'utf-8');
    }
    throw new Error('Tipo de dados não suportado para compressão');
  }

  /**
   * Calcula entropia dos dados para análise
   */
  private calculateEntropy(data: Buffer): number {
    const frequencies = new Map<number, number>();
    
    // Contar frequências
    for (const byte of data) {
      frequencies.set(byte, (frequencies.get(byte) || 0) + 1);
    }

    // Calcular entropia
    let entropy = 0;
    const length = data.length;

    for (const count of frequencies.values()) {
      const probability = count / length;
      entropy -= probability * Math.log2(probability);
    }

    return entropy / 8; // Normalizar para 0-1
  }
}

// Instância singleton do serviço de compressão
export const compressionService = new ExcelCompressionService();
