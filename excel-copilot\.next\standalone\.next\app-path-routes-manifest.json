{"/_not-found/page": "/_not-found", "/admin/health/page": "/admin/health", "/api-docs/page": "/api-docs", "/api/ai/status/route": "/api/ai/status", "/api/deprecated-usage/route": "/api/deprecated-usage", "/api/excel/ai-process/route": "/api/excel/ai-process", "/api/feedback/route": "/api/feedback", "/api/health/ai/route": "/api/health/ai", "/api/health/all/route": "/api/health/all", "/api/health/auth/route": "/api/health/auth", "/api/health/database/route": "/api/health/database", "/api/health/debug/route": "/api/health/debug", "/api/health/mcp/route": "/api/health/mcp", "/api/health/metrics/route": "/api/health/metrics", "/api/health/stripe/route": "/api/health/stripe", "/api/health/test/route": "/api/health/test", "/api/linear/issues/route": "/api/linear/issues", "/api/linear/status/route": "/api/linear/status", "/api/vercel/env/route": "/api/vercel/env", "/api/workbooks/[id]/collaborators/route": "/api/workbooks/[id]/collaborators", "/api/workbooks/[id]/duplicate/route": "/api/workbooks/[id]/duplicate", "/api/workbooks/[id]/storage/route": "/api/workbooks/[id]/storage", "/auth/signin/page": "/auth/signin", "/dashboard/account/page": "/dashboard/account", "/dashboard/analytics/page": "/dashboard/analytics", "/examples/page": "/examples", "/dashboard/page": "/dashboard", "/help/page": "/help", "/page": "/", "/profile/page": "/profile", "/robots.txt/route": "/robots.txt", "/settings/page": "/settings", "/templates/page": "/templates", "/workbook/[id]/page": "/workbook/[id]", "/sitemap.xml/route": "/sitemap.xml", "/api/admin/security-stats/route": "/api/admin/security-stats", "/api/admin/subscription-integrity/route": "/api/admin/subscription-integrity", "/api/auth-callback/route": "/api/auth-callback", "/api/analytics/vitals/route": "/api/analytics/vitals", "/api/api-docs/route": "/api/api-docs", "/api/auth/[...nextauth]/route": "/api/auth/[...next<PERSON>h]", "/api/auth/capture-oauth-error/route": "/api/auth/capture-oauth-error", "/api/auth/check-env/route": "/api/auth/check-env", "/api/auth/debug-google/route": "/api/auth/debug-google", "/api/auth/debug-providers/route": "/api/auth/debug-providers", "/api/auth/debug-flow/route": "/api/auth/debug-flow", "/api/auth/debug-oauth/route": "/api/auth/debug-oauth", "/api/auth/debug/route": "/api/auth/debug", "/api/auth/reset-rate-limit/route": "/api/auth/reset-rate-limit", "/api/auth/test-google/route": "/api/auth/test-google", "/api/auth/health/route": "/api/auth/health", "/api/auth/test-login/route": "/api/auth/test-login", "/api/billing/customer-portal/route": "/api/billing/customer-portal", "/api/auth/test-providers/route": "/api/auth/test-providers", "/api/auth/test-config/route": "/api/auth/test-config", "/api/chat/route": "/api/chat", "/api/csrf/route": "/api/csrf", "/api/checkout/route": "/api/checkout", "/api/db-status/route": "/api/db-status", "/api/checkout/trial/route": "/api/checkout/trial", "/api/excel/download/[id]/route": "/api/excel/download/[id]", "/api/excel/route": "/api/excel", "/api/github/status/route": "/api/github/status", "/api/github/repositories/route": "/api/github/repositories", "/api/github/issues/route": "/api/github/issues", "/api/github/workflows/route": "/api/github/workflows", "/api/health/db/route": "/api/health/db", "/api/health/route": "/api/health", "/api/legacy-redirect/[...path]/route": "/api/legacy-redirect/[...path]", "/api/metrics/route": "/api/metrics", "/api/migration-example/route": "/api/migration-example", "/api/linear/teams/route": "/api/linear/teams", "/api/stripe/status/route": "/api/stripe/status", "/api/socket/route": "/api/socket", "/api/stripe/customers/route": "/api/stripe/customers", "/api/supabase/status/route": "/api/supabase/status", "/api/stripe/payments/route": "/api/stripe/payments", "/api/trpc/[trpc]/route": "/api/trpc/[trpc]", "/api/stripe/subscriptions/route": "/api/stripe/subscriptions", "/api/user/api-usage/route": "/api/user/api-usage", "/api/supabase/tables/route": "/api/supabase/tables", "/api/supabase/storage/route": "/api/supabase/storage", "/api/user/subscription/route": "/api/user/subscription", "/api/user/profile/route": "/api/user/profile", "/api/vercel/deployments/route": "/api/vercel/deployments", "/api/vercel/status/route": "/api/vercel/status", "/api/workbook/save/route": "/api/workbook/save", "/api/vercel/logs/route": "/api/vercel/logs", "/api/webhooks/stripe/route": "/api/webhooks/stripe", "/api/workbooks/[id]/sheets/[sheetId]/chunks/route": "/api/workbooks/[id]/sheets/[sheetId]/chunks", "/api/workbooks/[id]/export/route": "/api/workbooks/[id]/export", "/api/workbooks/[id]/route": "/api/workbooks/[id]", "/api/workbooks/recent/route": "/api/workbooks/recent", "/api/workbooks/shared/route": "/api/workbooks/shared", "/api/workbooks/route": "/api/workbooks", "/api/ws/route": "/api/ws", "/privacy/page": "/privacy", "/terms/page": "/terms", "/workbook/new/page": "/workbook/new", "/pricing/page": "/pricing"}