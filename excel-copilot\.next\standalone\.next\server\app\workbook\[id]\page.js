(()=>{var e={};e.id=3338,e.ids=[3338],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},94007:e=>{"use strict";e.exports=require("@prisma/client")},23532:e=>{"use strict";e.exports=require("exceljs")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},32081:e=>{"use strict";e.exports=require("child_process")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},98188:e=>{"use strict";e.exports=require("module")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},85477:e=>{"use strict";e.exports=require("punycode")},63477:e=>{"use strict";e.exports=require("querystring")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},71267:e=>{"use strict";e.exports=require("worker_threads")},59796:e=>{"use strict";e.exports=require("zlib")},88684:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>c,routeModule:()=>f,tree:()=>l}),r(55694),r(65675),r(12523);var o=r(23191),n=r(88716),i=r(37922),s=r.n(i),a=r(95231),u={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);r.d(t,u);let l=["",{children:["workbook",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,55694)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\workbook\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,65675)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\not-found.tsx"]}],c=["C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\workbook\\[id]\\page.tsx"],d="/workbook/[id]/page",p={require:r,loadChunk:()=>Promise.resolve()},f=new o.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/workbook/[id]/page",pathname:"/workbook/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},97047:(e,t,r)=>{Promise.resolve().then(r.bind(r,31653))},36478:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var o=r(71159),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=(e,t)=>{let r=(0,o.forwardRef)(({color:r="currentColor",size:s=24,strokeWidth:a=2,absoluteStrokeWidth:u,children:l,...c},d)=>(0,o.createElement)("svg",{ref:d,...n,width:s,height:s,stroke:r,strokeWidth:u?24*Number(a)/Number(s):a,className:`lucide lucide-${i(e)}`,...c},[...t.map(([e,t])=>(0,o.createElement)(e,t)),...(Array.isArray(l)?l:[l])||[]]));return r.displayName=`${e}`,r}},58585:(e,t,r)=>{"use strict";var o=r(61085);r.o(o,"notFound")&&r.d(t,{notFound:function(){return o.notFound}}),r.o(o,"redirect")&&r.d(t,{redirect:function(){return o.redirect}})},61085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return s},RedirectType:function(){return o.RedirectType},notFound:function(){return n.notFound},permanentRedirect:function(){return o.permanentRedirect},redirect:function(){return o.redirect}});let o=r(83953),n=r(16399);class i extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class s extends URLSearchParams{append(){throw new i}delete(){throw new i}set(){throw new i}sort(){throw new i}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return n},notFound:function(){return o}});let r="NEXT_NOT_FOUND";function o(){let e=Error(r);throw e.digest=r,e}function n(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{"use strict";var o;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return o},getRedirectError:function(){return u},getRedirectStatusCodeFromError:function(){return m},getRedirectTypeFromError:function(){return f},getURLFromRedirectError:function(){return p},isRedirectError:function(){return d},permanentRedirect:function(){return c},redirect:function(){return l}});let n=r(54580),i=r(72934),s=r(8586),a="NEXT_REDIRECT";function u(e,t,r){void 0===r&&(r=s.RedirectStatusCode.TemporaryRedirect);let o=Error(a);o.digest=a+";"+t+";"+e+";"+r+";";let i=n.requestAsyncStorage.getStore();return i&&(o.mutableCookies=i.mutableCookies),o}function l(e,t){void 0===t&&(t="replace");let r=i.actionAsyncStorage.getStore();throw u(e,t,(null==r?void 0:r.isAction)?s.RedirectStatusCode.SeeOther:s.RedirectStatusCode.TemporaryRedirect)}function c(e,t){void 0===t&&(t="replace");let r=i.actionAsyncStorage.getStore();throw u(e,t,(null==r?void 0:r.isAction)?s.RedirectStatusCode.SeeOther:s.RedirectStatusCode.PermanentRedirect)}function d(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,o,n]=e.digest.split(";",4),i=Number(n);return t===a&&("replace"===r||"push"===r)&&"string"==typeof o&&!isNaN(i)&&i in s.RedirectStatusCode}function p(e){return d(e)?e.digest.split(";",3)[2]:null}function f(e){if(!d(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function m(e){if(!d(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(o||(o={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79925:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,i={};function s(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),o=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?o:`${o}; ${r.join("; ")}`}function a(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[o,n]=[r.slice(0,e),r.slice(e+1)];try{t.set(o,decodeURIComponent(null!=n?n:"true"))}catch{}}return t}function u(e){var t,r;if(!e)return;let[[o,n],...i]=a(e),{domain:s,expires:u,httponly:d,maxage:p,path:f,samesite:m,secure:h,partitioned:g,priority:y}=Object.fromEntries(i.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:o,value:decodeURIComponent(n),domain:s,...u&&{expires:new Date(u)},...d&&{httpOnly:!0},..."string"==typeof p&&{maxAge:Number(p)},path:f,...m&&{sameSite:l.includes(t=(t=m).toLowerCase())?t:void 0},...h&&{secure:!0},...y&&{priority:c.includes(r=(r=y).toLowerCase())?r:void 0},...g&&{partitioned:!0}})}((e,r)=>{for(var o in r)t(e,o,{get:r[o],enumerable:!0})})(i,{RequestCookies:()=>d,ResponseCookies:()=>p,parseCookie:()=>a,parseSetCookie:()=>u,stringifyCookie:()=>s}),e.exports=((e,i,s,a)=>{if(i&&"object"==typeof i||"function"==typeof i)for(let s of o(i))n.call(e,s)||void 0===s||t(e,s,{get:()=>i[s],enumerable:!(a=r(i,s))||a.enumerable});return e})(t({},"__esModule",{value:!0}),i);var l=["strict","lax","none"],c=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of a(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let o="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===o).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,o=this._parsed;return o.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(o).map(([e,t])=>s(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>s(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},p=class{constructor(e){var t,r,o;this._parsed=new Map,this._headers=e;let n=null!=(o=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?o:[];for(let e of Array.isArray(n)?n:function(e){if(!e)return[];var t,r,o,n,i,s=[],a=0;function u(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,i=!1;u();)if(","===(r=e.charAt(a))){for(o=a,a+=1,u(),n=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(i=!0,a=n,s.push(e.substring(t,o)),t=a):a=o+1}else a+=1;(!i||a>=e.length)&&s.push(e.substring(t,e.length))}return s}(n)){let t=u(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let o="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===o)}has(e){return this._parsed.has(e)}set(...e){let[t,r,o]=1===e.length?[e[0].name,e[0].value,e[0]]:e,n=this._parsed;return n.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...o})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=s(r);t.append("set-cookie",e)}}(n,this._headers),this}delete(...e){let[t,r,o]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:o,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(s).join("; ")}}},92044:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return o.RequestCookies},ResponseCookies:function(){return o.ResponseCookies}});let o=r(79925)},55694:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g,generateMetadata:()=>m});var o=r(19510);let n=(0,r(36478).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);var i=r(58585),s=r(45609),a=r(71159),u=r(68570);let l=(0,u.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\components\workbook\SpreadsheetEditor.tsx`),{__esModule:c,$$typeof:d}=l;l.default;let p=(0,u.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\components\workbook\SpreadsheetEditor.tsx#SpreadsheetEditor`);var f=r(63841);async function m({params:e}){try{let t=await f.prisma.workbook.findUnique({where:{id:e.id},select:{name:!0,description:!0}});if(!t)return{title:"Planilha n\xe3o encontrada"};return{title:`${t.name} | Excel Copilot`,description:t.description||"Edite sua planilha com comandos em linguagem natural"}}catch{return{title:"Planilha | Excel Copilot"}}}async function h(e){let t=await (0,s.getServerSession)();if(!t||!t.user)return(0,i.redirect)("/auth/signin?callbackUrl=/workbook/"+e),null;try{let r=await f.prisma.workbook.findUnique({where:{id:e,userId:t.user.id},include:{sheets:!0}});if(!r)return null;return r}catch(e){return console.error("Erro ao buscar workbook:",e),null}}async function g({params:e,searchParams:t}){let r=await h(e.id);r||(0,i.notFound)();let s=t?.command,u=r.sheets[0],l={headers:["A","B","C"],rows:[["","",""],["","",""],["","",""]],charts:[],name:u?.name||"Nova Planilha"};if(u&&u.data)try{let e=JSON.parse(u.data);l={headers:e.headers||["A","B","C"],rows:e.rows||[["","",""],["","",""],["","",""]],charts:e.charts||[],name:u.name}}catch(e){console.error("Erro ao analisar dados da planilha:",e)}return o.jsx("div",{className:"w-full h-[calc(100vh-64px)] flex flex-col",children:o.jsx(a.Suspense,{fallback:(0,o.jsxs)("div",{className:"h-full w-full flex items-center justify-center",children:[o.jsx(n,{className:"h-8 w-8 animate-spin text-primary"}),o.jsx("span",{className:"ml-2",children:"Carregando planilha..."})]}),children:o.jsx(p,{workbookId:e.id,initialData:l,initialCommand:s})})})}},63841:(e,t,r)=>{"use strict";r.d(t,{P:()=>u,prisma:()=>a});var o=r(53524);let n={info:(e,...t)=>{},error:(e,...t)=>{console.error(`[DB ERROR] ${e}`,...t)},warn:(e,...t)=>{console.warn(`[DB WARNING] ${e}`,...t)}},i={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},s=[],a=global.prisma||new o.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function u(){return{...i,activeConnections:Math.min(Math.floor(5*Math.random())+1,i.maxPoolSize),poolSize:i.poolSize}}async function l(){try{await a.$disconnect(),n.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){n.error("Erro ao desconectar do banco de dados",e)}}a.$on("query",e=>{i.totalQueries++,e.duration&&(s.push(e.duration),s.length>100&&s.shift(),i.averageQueryTime=s.reduce((e,t)=>e+t,0)/s.length),e.duration&&e.duration>500&&n.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),a.$on("error",e=>{i.failedQueries++,i.connectionFailures++,i.lastConnectionFailure=new Date().toISOString(),n.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{l()})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,9557,7410,330,5609,86,7915,5999,4002,5108,6068,922,2972,4433,6841,9361,1653],()=>r(88684));module.exports=o})();