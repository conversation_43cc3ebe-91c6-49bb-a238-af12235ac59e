/**
 * Sistema de Cache Inteligente para IA
 * Implementa cache contextual baseado em workbook, operação e conteúdo
 */

import { CacheManager } from '@/lib/cache-manager';
import { logger } from '@/lib/logger';

export interface AICacheKey {
  userId: string;
  workbookId: string | undefined;
  operationType: string;
  contentHash: string;
  model: string;
}

export interface AICacheEntry {
  response: string;
  metadata: {
    model: string;
    tokensUsed: {
      input: number;
      output: number;
      total: number;
    };
    responseTime: number;
    timestamp: Date;
    operationType: string;
    workbookContext: {
      id: string;
      version: string;
      sheetCount: number;
      lastModified: Date;
    } | undefined;
  };
}

export interface AICacheConfig {
  defaultTTL: number;
  maxSize: number;
  enableContextualInvalidation: boolean;
  enableStaleWhileRevalidate: boolean;
  compressionEnabled: boolean;
}

/**
 * Gerenciador de Cache Contextual para IA
 */
export class AICacheManager {
  private static instance: AICacheManager;
  private cache: CacheManager<AICacheEntry>;
  private config: AICacheConfig;
  private workbookVersions = new Map<string, string>();

  private constructor() {
    this.config = {
      defaultTTL: 24 * 60 * 60 * 1000, // 24 horas
      maxSize: 500,
      enableContextualInvalidation: true,
      enableStaleWhileRevalidate: true,
      compressionEnabled: true,
    };

    this.cache = new CacheManager<AICacheEntry>({
      maxSize: this.config.maxSize,
      defaultTTL: this.config.defaultTTL,
      enableStaleWhileRevalidate: this.config.enableStaleWhileRevalidate,
      namespace: 'ai-cache',
    });
  }

  public static getInstance(): AICacheManager {
    if (!AICacheManager.instance) {
      AICacheManager.instance = new AICacheManager();
    }
    return AICacheManager.instance;
  }

  /**
   * Gera chave de cache baseada no contexto
   */
  private generateCacheKey(key: AICacheKey): string {
    const parts = [
      key.userId,
      key.workbookId || 'global',
      key.operationType,
      key.model,
      key.contentHash,
    ];
    
    return parts.join(':');
  }

  /**
   * Gera hash do conteúdo para cache
   */
  private generateContentHash(content: string, context?: any): string {
    const fullContent = JSON.stringify({ content, context });
    
    // Implementação simples de hash (em produção, usar crypto)
    let hash = 0;
    for (let i = 0; i < fullContent.length; i++) {
      const char = fullContent.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Converter para 32bit integer
    }
    
    return Math.abs(hash).toString(36);
  }

  /**
   * Obtém resposta do cache
   */
  public async get(
    userId: string,
    prompt: string,
    operationType: string,
    model: string,
    workbookContext?: {
      id: string;
      version: string;
      sheetCount: number;
      lastModified: Date;
    }
  ): Promise<AICacheEntry | null> {
    try {
      const contentHash = this.generateContentHash(prompt, workbookContext);
      const cacheKey = this.generateCacheKey({
        userId,
        workbookId: workbookContext?.id,
        operationType,
        contentHash,
        model,
      });

      // Verificar se o workbook foi modificado (invalidação contextual)
      if (this.config.enableContextualInvalidation && workbookContext) {
        const cachedVersion = this.workbookVersions.get(workbookContext.id);
        if (cachedVersion && cachedVersion !== workbookContext.version) {
          // Workbook foi modificado, invalidar cache relacionado
          await this.invalidateWorkbookCache(workbookContext.id);
          this.workbookVersions.set(workbookContext.id, workbookContext.version);
          return null;
        }
      }

      const cached = this.cache.get<AICacheEntry>(cacheKey);
      
      if (cached) {
        logger.debug(`Cache hit para IA: ${operationType} - ${userId}`);
        
        // Verificar se o contexto do workbook ainda é válido
        if (workbookContext && cached.metadata.workbookContext) {
          const cachedContext = cached.metadata.workbookContext;
          
          // Se o workbook foi modificado após o cache, invalidar
          if (workbookContext.lastModified > cachedContext.lastModified) {
            logger.debug(`Cache invalidado por modificação do workbook: ${workbookContext.id}`);
            this.cache.delete(cacheKey);
            return null;
          }
        }
        
        return cached;
      }

      return null;

    } catch (error) {
      logger.error('Erro ao obter do cache de IA:', error);
      return null;
    }
  }

  /**
   * Armazena resposta no cache
   */
  public async set(
    userId: string,
    prompt: string,
    response: string,
    operationType: string,
    model: string,
    metadata: {
      tokensUsed: { input: number; output: number; total: number };
      responseTime: number;
    },
    workbookContext?: {
      id: string;
      version: string;
      sheetCount: number;
      lastModified: Date;
    },
    customTTL?: number
  ): Promise<void> {
    try {
      const contentHash = this.generateContentHash(prompt, workbookContext);
      const cacheKey = this.generateCacheKey({
        userId,
        workbookId: workbookContext?.id,
        operationType,
        contentHash,
        model,
      });

      const cacheEntry: AICacheEntry = {
        response: this.config.compressionEnabled ? this.compressResponse(response) : response,
        metadata: {
          model,
          tokensUsed: metadata.tokensUsed,
          responseTime: metadata.responseTime,
          timestamp: new Date(),
          operationType,
          workbookContext,
        },
      };

      // Determinar TTL baseado no tipo de operação
      const ttl = customTTL || this.getTTLForOperation(operationType);
      
      // Armazenar no cache
      this.cache.set(cacheKey, cacheEntry, ttl);

      // Atualizar versão do workbook se fornecida
      if (workbookContext) {
        this.workbookVersions.set(workbookContext.id, workbookContext.version);
      }

      logger.debug(`Cache armazenado para IA: ${operationType} - ${userId} - TTL: ${ttl}ms`);

    } catch (error) {
      logger.error('Erro ao armazenar no cache de IA:', error);
    }
  }

  /**
   * Invalida cache de um workbook específico
   */
  public async invalidateWorkbookCache(workbookId: string): Promise<void> {
    try {
      // Como não temos acesso direto às chaves, vamos marcar para limpeza
      // Em uma implementação mais robusta, manteríamos um índice de chaves por workbook
      logger.info(`Invalidando cache do workbook: ${workbookId}`);
      
      // Remover versão cached
      this.workbookVersions.delete(workbookId);
      
      // Nota: Em uma implementação completa, iteraríamos sobre as chaves
      // e removeríamos aquelas que contêm o workbookId
      
    } catch (error) {
      logger.error('Erro ao invalidar cache do workbook:', error);
    }
  }

  /**
   * Invalida cache de um usuário específico
   */
  public async invalidateUserCache(userId: string): Promise<void> {
    try {
      logger.info(`Invalidando cache do usuário: ${userId}`);
      // Implementação similar à invalidação de workbook
      
    } catch (error) {
      logger.error('Erro ao invalidar cache do usuário:', error);
    }
  }

  /**
   * Obtém TTL baseado no tipo de operação
   */
  private getTTLForOperation(operationType: string): number {
    const ttlMap: Record<string, number> = {
      'chat': 30 * 60 * 1000, // 30 minutos - conversas podem mudar rapidamente
      'analysis': 2 * 60 * 60 * 1000, // 2 horas - análises são mais estáveis
      'generation': 60 * 60 * 1000, // 1 hora - gerações podem ser reutilizadas
      'translation': 24 * 60 * 60 * 1000, // 24 horas - traduções são estáveis
      'formula': 4 * 60 * 60 * 1000, // 4 horas - fórmulas são relativamente estáveis
    };

    return ttlMap[operationType] || this.config.defaultTTL;
  }

  /**
   * Comprime resposta para economizar memória
   */
  private compressResponse(response: string): string {
    // Implementação simples - em produção, usar gzip ou similar
    return response.length > 1000 ? response.substring(0, 1000) + '...[compressed]' : response;
  }

  /**
   * Descomprime resposta
   */
  private decompressResponse(response: string): string {
    // Implementação simples - em produção, usar gzip ou similar
    return response;
  }

  /**
   * Obtém estatísticas do cache
   */
  public getStats(): any {
    return {
      cacheStats: {
        size: this.cache['cache']?.size || 0,
        maxSize: this.config.maxSize,
      },
      workbookVersions: this.workbookVersions.size,
      config: this.config,
    };
  }

  /**
   * Limpa todo o cache
   */
  public clear(): void {
    this.cache.clear();
    this.workbookVersions.clear();
  }
}
