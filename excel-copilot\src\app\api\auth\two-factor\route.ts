import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';

import { authOptions } from '@/server/auth/options';
import { logger } from '@/lib/logger';
import { 
  enableTwoFactorForUser, 
  disableTwoFactorForUser, 
  isTwoFactorEnabled,
  sendTwoFactorCodeByEmail,
  verifyTwoFactorCode,
  requiresTwoFactor
} from '@/lib/auth/two-factor';
import { ApiResponse } from '@/utils/api-response';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

/**
 * GET /api/auth/two-factor
 * Verifica status do 2FA para o usuário atual
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Não autenticado' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const isEnabled = await isTwoFactorEnabled(userId);
    const isRequired = await requiresTwoFactor(userId);

    return ApiResponse.success({
      isEnabled,
      isRequired,
      userId,
    });
  } catch (error) {
    logger.error('Erro ao verificar status 2FA', { error });
    return ApiResponse.error('Erro interno do servidor');
  }
}

/**
 * POST /api/auth/two-factor
 * Habilita 2FA para o usuário atual
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Não autenticado' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const body = await request.json();
    const { action, code } = body;

    switch (action) {
      case 'enable':
        const result = await enableTwoFactorForUser(userId);
        if (!result) {
          return ApiResponse.error('Erro ao habilitar 2FA');
        }
        
        logger.info('✅ 2FA habilitado via API', { userId });
        return ApiResponse.success({
          message: '2FA habilitado com sucesso',
          secret: result.secret,
          backupCodes: result.backupCodes,
        });

      case 'send-code':
        if (!session.user.email) {
          return ApiResponse.error('Email não encontrado na sessão');
        }
        
        const sent = await sendTwoFactorCodeByEmail(userId, session.user.email);
        if (!sent) {
          return ApiResponse.error('Erro ao enviar código 2FA');
        }
        
        return ApiResponse.success({
          message: 'Código 2FA enviado por email',
        });

      case 'verify-code':
        if (!code) {
          return ApiResponse.error('Código é obrigatório');
        }
        
        const isValid = await verifyTwoFactorCode(userId, code);
        if (!isValid) {
          return ApiResponse.error('Código inválido ou expirado');
        }
        
        return ApiResponse.success({
          message: 'Código verificado com sucesso',
          verified: true,
        });

      default:
        return ApiResponse.error('Ação não suportada');
    }
  } catch (error) {
    logger.error('Erro na API 2FA', { error });
    return ApiResponse.error('Erro interno do servidor');
  }
}

/**
 * DELETE /api/auth/two-factor
 * Desabilita 2FA para o usuário atual
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Não autenticado' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    
    // Verificar se o usuário é admin (admins não podem desabilitar 2FA)
    const isRequired = await requiresTwoFactor(userId);
    if (isRequired) {
      return ApiResponse.error('Usuários administrativos não podem desabilitar 2FA');
    }

    const success = await disableTwoFactorForUser(userId);
    if (!success) {
      return ApiResponse.error('Erro ao desabilitar 2FA');
    }

    logger.info('✅ 2FA desabilitado via API', { userId });
    return ApiResponse.success({
      message: '2FA desabilitado com sucesso',
    });
  } catch (error) {
    logger.error('Erro ao desabilitar 2FA', { error });
    return ApiResponse.error('Erro interno do servidor');
  }
}
