{"/_not-found/page": "app/_not-found/page.js", "/admin/health/page": "app/admin/health/page.js", "/api-docs/page": "app/api-docs/page.js", "/api/ai/status/route": "app/api/ai/status/route.js", "/api/deprecated-usage/route": "app/api/deprecated-usage/route.js", "/api/excel/ai-process/route": "app/api/excel/ai-process/route.js", "/api/feedback/route": "app/api/feedback/route.js", "/api/health/ai/route": "app/api/health/ai/route.js", "/api/health/all/route": "app/api/health/all/route.js", "/api/health/auth/route": "app/api/health/auth/route.js", "/api/health/database/route": "app/api/health/database/route.js", "/api/health/debug/route": "app/api/health/debug/route.js", "/api/health/mcp/route": "app/api/health/mcp/route.js", "/api/health/metrics/route": "app/api/health/metrics/route.js", "/api/health/stripe/route": "app/api/health/stripe/route.js", "/api/health/test/route": "app/api/health/test/route.js", "/api/linear/issues/route": "app/api/linear/issues/route.js", "/api/linear/status/route": "app/api/linear/status/route.js", "/api/vercel/env/route": "app/api/vercel/env/route.js", "/api/workbooks/[id]/collaborators/route": "app/api/workbooks/[id]/collaborators/route.js", "/api/workbooks/[id]/duplicate/route": "app/api/workbooks/[id]/duplicate/route.js", "/api/workbooks/[id]/storage/route": "app/api/workbooks/[id]/storage/route.js", "/auth/signin/page": "app/auth/signin/page.js", "/dashboard/account/page": "app/dashboard/account/page.js", "/dashboard/analytics/page": "app/dashboard/analytics/page.js", "/examples/page": "app/examples/page.js", "/dashboard/page": "app/dashboard/page.js", "/help/page": "app/help/page.js", "/page": "app/page.js", "/profile/page": "app/profile/page.js", "/robots.txt/route": "app/robots.txt/route.js", "/settings/page": "app/settings/page.js", "/templates/page": "app/templates/page.js", "/workbook/[id]/page": "app/workbook/[id]/page.js", "/sitemap.xml/route": "app/sitemap.xml/route.js", "/api/admin/security-stats/route": "app/api/admin/security-stats/route.js", "/api/admin/subscription-integrity/route": "app/api/admin/subscription-integrity/route.js", "/api/auth-callback/route": "app/api/auth-callback/route.js", "/api/analytics/vitals/route": "app/api/analytics/vitals/route.js", "/api/api-docs/route": "app/api/api-docs/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/auth/capture-oauth-error/route": "app/api/auth/capture-oauth-error/route.js", "/api/auth/check-env/route": "app/api/auth/check-env/route.js", "/api/auth/debug-google/route": "app/api/auth/debug-google/route.js", "/api/auth/debug-providers/route": "app/api/auth/debug-providers/route.js", "/api/auth/debug-flow/route": "app/api/auth/debug-flow/route.js", "/api/auth/debug-oauth/route": "app/api/auth/debug-oauth/route.js", "/api/auth/debug/route": "app/api/auth/debug/route.js", "/api/auth/reset-rate-limit/route": "app/api/auth/reset-rate-limit/route.js", "/api/auth/test-google/route": "app/api/auth/test-google/route.js", "/api/auth/health/route": "app/api/auth/health/route.js", "/api/auth/test-login/route": "app/api/auth/test-login/route.js", "/api/billing/customer-portal/route": "app/api/billing/customer-portal/route.js", "/api/auth/test-providers/route": "app/api/auth/test-providers/route.js", "/api/auth/test-config/route": "app/api/auth/test-config/route.js", "/api/chat/route": "app/api/chat/route.js", "/api/csrf/route": "app/api/csrf/route.js", "/api/checkout/route": "app/api/checkout/route.js", "/api/db-status/route": "app/api/db-status/route.js", "/api/checkout/trial/route": "app/api/checkout/trial/route.js", "/api/excel/download/[id]/route": "app/api/excel/download/[id]/route.js", "/api/excel/route": "app/api/excel/route.js", "/api/github/status/route": "app/api/github/status/route.js", "/api/github/repositories/route": "app/api/github/repositories/route.js", "/api/github/issues/route": "app/api/github/issues/route.js", "/api/github/workflows/route": "app/api/github/workflows/route.js", "/api/health/db/route": "app/api/health/db/route.js", "/api/health/route": "app/api/health/route.js", "/api/legacy-redirect/[...path]/route": "app/api/legacy-redirect/[...path]/route.js", "/api/metrics/route": "app/api/metrics/route.js", "/api/migration-example/route": "app/api/migration-example/route.js", "/api/linear/teams/route": "app/api/linear/teams/route.js", "/api/stripe/status/route": "app/api/stripe/status/route.js", "/api/socket/route": "app/api/socket/route.js", "/api/stripe/customers/route": "app/api/stripe/customers/route.js", "/api/supabase/status/route": "app/api/supabase/status/route.js", "/api/stripe/payments/route": "app/api/stripe/payments/route.js", "/api/trpc/[trpc]/route": "app/api/trpc/[trpc]/route.js", "/api/stripe/subscriptions/route": "app/api/stripe/subscriptions/route.js", "/api/user/api-usage/route": "app/api/user/api-usage/route.js", "/api/supabase/tables/route": "app/api/supabase/tables/route.js", "/api/supabase/storage/route": "app/api/supabase/storage/route.js", "/api/user/subscription/route": "app/api/user/subscription/route.js", "/api/user/profile/route": "app/api/user/profile/route.js", "/api/vercel/deployments/route": "app/api/vercel/deployments/route.js", "/api/vercel/status/route": "app/api/vercel/status/route.js", "/api/workbook/save/route": "app/api/workbook/save/route.js", "/api/vercel/logs/route": "app/api/vercel/logs/route.js", "/api/webhooks/stripe/route": "app/api/webhooks/stripe/route.js", "/api/workbooks/[id]/sheets/[sheetId]/chunks/route": "app/api/workbooks/[id]/sheets/[sheetId]/chunks/route.js", "/api/workbooks/[id]/export/route": "app/api/workbooks/[id]/export/route.js", "/api/workbooks/[id]/route": "app/api/workbooks/[id]/route.js", "/api/workbooks/recent/route": "app/api/workbooks/recent/route.js", "/api/workbooks/shared/route": "app/api/workbooks/shared/route.js", "/api/workbooks/route": "app/api/workbooks/route.js", "/api/ws/route": "app/api/ws/route.js", "/privacy/page": "app/privacy/page.js", "/terms/page": "app/terms/page.js", "/workbook/new/page": "app/workbook/new/page.js", "/pricing/page": "app/pricing/page.js"}