import { useState } from 'react';
import { toast } from 'sonner';

// Importar implementações reais de Excel
import {
  createExcelFile,
  downloadExcelFile,
  parseExcelFile,
  isValidExcelFile,
  exportToCSV
} from '@/lib/excel';

// Importar novos serviços de otimização
import { backupService } from '@/lib/excel/backup-service';
import { compressionService } from '@/lib/excel/compression-service';
import { versionService } from '@/lib/excel/version-service';
import { performanceOptimizer } from '@/lib/excel/performance-optimizer';

interface ExcelSheet {
  name: string;
  data: unknown;
}

type ExportFormat = 'xlsx' | 'csv';

/**
 * Hook para facilitar operações com arquivos Excel
 */
export function useExcelFile() {
  const [isLoading, setIsLoading] = useState(false);

  /**
   * Importa um arquivo Excel com funcionalidades avançadas
   * @param file Arquivo a ser importado
   * @param options Opções de importação
   * @returns Promise com os dados das planilhas ou null em caso de erro
   */
  const importExcel = async (
    file: File,
    options: {
      onSuccess?: (data: { fileName: string; sheets: ExcelSheet[] }) => void;
      maxSize?: number; // em bytes
      trackAnalytics?: boolean;
      template?: string | null; // Template de importação
      validateSchema?: boolean; // Validar schema dos dados
      transformData?: boolean; // Aplicar transformações
      onProgress?: (progress: number) => void; // Callback de progresso
      enableBackup?: boolean; // Criar backup automático
      enableCompression?: boolean; // Aplicar compressão
      enablePerformanceOptimization?: boolean; // Otimizar performance
      workbookId?: string; // ID do workbook para backup/versionamento
      userId?: string; // ID do usuário
    } = {}
  ): Promise<{ fileName: string; sheets: ExcelSheet[] } | null> => {
    if (!file) return null;

    const maxSize = options.maxSize || 10 * 1024 * 1024; // 10MB default

    setIsLoading(true);
    const toastId = toast.loading(`Processando arquivo ${file.name}...`);

    try {
      // Verificações básicas
      if (!isValidExcelFile(file)) {
        throw new Error('Formato inválido: envie um arquivo Excel (.xlsx ou .xls)');
      }

      if (file.size > maxSize) {
        throw new Error(
          `Arquivo muito grande: o tamanho máximo é ${(maxSize / (1024 * 1024)).toFixed(0)}MB`
        );
      }

      // Callback de progresso inicial
      options.onProgress?.(10);

      // Processar o arquivo Excel com otimização de performance se habilitado
      let sheets: ExcelSheet[];

      if (options.enablePerformanceOptimization && file.size > 1024 * 1024) { // > 1MB
        // Usar otimizador de performance para arquivos grandes
        const fileBuffer = await file.arrayBuffer();
        const data = new Uint8Array(fileBuffer);

        // Processar com otimização
        const result = await performanceOptimizer.processLargeDataset(
          [Array.from(data)], // Converter para formato processável
          async (chunk) => {
            // Processar chunk do arquivo
            if (chunk[0] && Array.isArray(chunk[0])) {
              const chunkFile = new File([new Uint8Array(chunk[0] as number[])], file.name, { type: file.type });
              return await parseExcelFile(chunkFile);
            }
            return [];
          },
          {
            chunkSize: 1000,
            enableCache: true,
            enableLazyLoading: true,
          },
          (progress) => options.onProgress?.(20 + (progress * 0.3)) // 20-50% do progresso
        );

        sheets = result.data.flat();
      } else {
        // Processamento normal
        sheets = await parseExcelFile(file);
      }

      options.onProgress?.(50);

      // Verificar se obteve dados
      if (!sheets || sheets.length === 0) {
        throw new Error('O arquivo não contém dados válidos');
      }

      // Aplicar validação de schema se solicitado
      if (options.validateSchema && options.template) {
        await validateDataSchema(sheets, options.template);
        options.onProgress?.(70);
      }

      // Aplicar transformações se solicitado
      if (options.transformData && options.template) {
        await transformDataByTemplate(sheets, options.template);
        options.onProgress?.(80);
      }

      // Criar backup automático se solicitado
      if (options.enableBackup && options.workbookId && options.userId) {
        try {
          const backupResult = await backupService.createBackup({
            workbookId: options.workbookId,
            userId: options.userId,
            data: { fileName: file.name, sheets },
            description: `Backup automático de importação - ${file.name}`,
            autoCleanup: true,
          });

          if (backupResult.success) {
            toast.success('Backup automático criado', {
              description: `Versão ${backupResult.version} salva com segurança`,
              duration: 2000,
            });
          }
        } catch (error) {
          console.warn('Erro ao criar backup automático:', error);
          // Não falhar a importação por causa do backup
        }
        options.onProgress?.(90);
      }

      // Sucesso
      toast.success(`${file.name} carregado com sucesso!`, {
        id: toastId,
        duration: 3000,
      });

      // Progresso final
      options.onProgress?.(100);

      // Registrar evento de importação (analytics)
      if (options.trackAnalytics && typeof window !== 'undefined' && 'gtag' in window) {
        const windowWithGtag = window as unknown as import('@/types/global-types').WindowWithGtag;
        const gtag = windowWithGtag.gtag;
        if (typeof gtag === 'function') {
          gtag('event', 'import_excel_advanced', {
            file_size: file.size,
            file_type: file.type,
            sheet_count: sheets.length,
            template_used: options.template || 'none',
            validation_enabled: options.validateSchema || false,
            transformation_enabled: options.transformData || false,
          });
        }
      }

      const result = {
        fileName: file.name,
        sheets: sheets,
      };

      // Callback de sucesso se fornecido
      if (options.onSuccess) {
        options.onSuccess(result);
      }

      return result;
    } catch (error) {
      console.error('Erro ao processar arquivo Excel:', error);
      toast.error('Erro ao importar arquivo', {
        id: toastId,
        description:
          error instanceof Error
            ? error.message
            : 'Não foi possível processar o arquivo. Tente novamente.',
        duration: 4000,
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Exporta dados para um arquivo Excel ou CSV
   * @param sheets Array de planilhas com dados
   * @param fileName Nome do arquivo sem extensão
   * @param format Formato de exportação (xlsx ou csv)
   * @param options Opções adicionais
   * @returns Promise<boolean> indicando sucesso ou falha
   */
  const exportExcel = async (
    sheets: ExcelSheet[],
    fileName: string,
    format: ExportFormat = 'xlsx',
    options: {
      trackAnalytics?: boolean;
      workbookId?: string;
      userId?: string;
      enableCompression?: boolean;
      enableVersioning?: boolean;
      versionDescription?: string;
      author?: string;
    } = {}
  ): Promise<boolean> => {
    setIsLoading(true);

    // Remover caracteres especiais e espaços do nome do arquivo
    const safeFileName = fileName.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const timestamp = Date.now();
    const fullFileName = `${safeFileName}_${timestamp}`;

    const toastId = toast.loading(`Preparando exportação ${format.toUpperCase()}...`);

    try {
      // Verificar se temos pelo menos uma planilha com dados
      if (!sheets || sheets.length === 0) {
        throw new Error('Não há dados para exportar');
      }

      // Exportar com base no formato
      if (format === 'xlsx') {
        // Gerar o arquivo Excel
        let blob = await createExcelFile(sheets, fileName);

        // Aplicar compressão se solicitado e arquivo for grande
        if (options.enableCompression && blob.size > 1024 * 1024) { // > 1MB
          try {
            const buffer = await blob.arrayBuffer();
            const compressionResult = await compressionService.compressData(
              new Uint8Array(buffer),
              compressionService.analyzeCompressionStrategy(new Uint8Array(buffer))
            );

            if (compressionResult.compressed) {
              blob = new Blob([compressionResult.data], { type: blob.type });
              toast.success('Arquivo comprimido', {
                description: `Tamanho reduzido em ${Math.round((1 - compressionResult.compressionRatio) * 100)}%`,
                duration: 2000,
              });
            }
          } catch (error) {
            console.warn('Erro na compressão, usando arquivo original:', error);
          }
        }

        // Criar versão se solicitado
        if (options.enableVersioning && options.workbookId && options.userId) {
          try {
            const versionInfo = await versionService.createVersion(
              options.workbookId,
              options.userId,
              { fileName, sheets },
              options.versionDescription || `Exportação ${format.toUpperCase()} - ${fileName}`,
              options.author || 'Usuário',
            );

            toast.success('Nova versão criada', {
              description: `Versão ${versionInfo.version} salva`,
              duration: 2000,
            });
          } catch (error) {
            console.warn('Erro ao criar versão:', error);
          }
        }

        // Fazer download do arquivo
        downloadExcelFile(blob, `${fullFileName}.xlsx`);

        // Registrar evento de exportação (analytics)
        if (options.trackAnalytics && typeof window !== 'undefined' && 'gtag' in window) {
          const windowWithGtag = window as unknown as import('@/types/global-types').WindowWithGtag;
          const gtag = windowWithGtag.gtag;
          if (typeof gtag === 'function') {
            gtag('event', 'export_excel', {
              workbook_id: options.workbookId,
              sheet_count: sheets.length,
              format: 'xlsx',
            });
          }
        }
      } else if (format === 'csv') {
        // Exportar como CSV
        exportToCSV(sheets, fullFileName);

        // Registrar evento de exportação (analytics)
        if (options.trackAnalytics && typeof window !== 'undefined' && 'gtag' in window) {
          const windowWithGtag = window as unknown as import('@/types/global-types').WindowWithGtag;
          const gtag = windowWithGtag.gtag;
          if (typeof gtag === 'function') {
            gtag('event', 'export_csv', {
              workbook_id: options.workbookId,
              sheet_count: sheets.length,
              format: 'csv',
            });
          }
        }
      }

      // Mostrar mensagem de sucesso
      toast.success(`Exportação ${format.toUpperCase()} concluída`, {
        id: toastId,
        description: `Arquivo "${fullFileName}.${format}" baixado com sucesso!`,
        duration: 3000,
      });

      return true;
    } catch (error) {
      console.error(`Erro ao exportar ${format}:`, error);
      toast.error(`Erro na exportação ${format.toUpperCase()}`, {
        id: toastId,
        description:
          error instanceof Error
            ? error.message
            : `Não foi possível exportar para ${format.toUpperCase()}. Tente novamente.`,
        duration: 4000,
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Lista versões disponíveis de um workbook
   */
  const listVersions = async (workbookId: string, userId: string) => {
    try {
      return await versionService.listVersions(workbookId, userId);
    } catch (error) {
      console.error('Erro ao listar versões:', error);
      toast.error('Erro ao carregar versões');
      return [];
    }
  };

  /**
   * Restaura uma versão específica
   */
  const restoreVersion = async (workbookId: string, userId: string, version: string) => {
    setIsLoading(true);
    try {
      const data = await versionService.getVersionData(workbookId, userId, version);
      toast.success(`Versão ${version} restaurada com sucesso`);
      return data;
    } catch (error) {
      console.error('Erro ao restaurar versão:', error);
      toast.error('Erro ao restaurar versão');
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Lista backups disponíveis
   */
  const listBackups = async (workbookId: string, userId: string) => {
    try {
      return await backupService.listBackups(workbookId, userId);
    } catch (error) {
      console.error('Erro ao listar backups:', error);
      toast.error('Erro ao carregar backups');
      return [];
    }
  };

  /**
   * Restaura um backup específico
   */
  const restoreBackup = async (backupId: string, workbookId: string, userId: string) => {
    setIsLoading(true);
    try {
      const data = await backupService.restoreBackup(backupId, workbookId, userId);
      toast.success('Backup restaurado com sucesso');
      return data;
    } catch (error) {
      console.error('Erro ao restaurar backup:', error);
      toast.error('Erro ao restaurar backup');
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Calcula diferenças entre duas versões
   */
  const compareVersions = async (
    workbookId: string,
    userId: string,
    version1: string,
    version2: string
  ) => {
    try {
      return await versionService.calculateDiff(workbookId, userId, version1, version2);
    } catch (error) {
      console.error('Erro ao comparar versões:', error);
      toast.error('Erro ao comparar versões');
      return null;
    }
  };

  /**
   * Obtém estatísticas de performance do cache
   */
  const getCacheStats = () => {
    return performanceOptimizer.getCacheStats();
  };

  return {
    isLoading,
    importExcel,
    exportExcel,
    listVersions,
    restoreVersion,
    listBackups,
    restoreBackup,
    compareVersions,
    getCacheStats,
  };
}

// Funções auxiliares para funcionalidades avançadas

/**
 * Valida schema dos dados baseado no template com validação avançada
 */
async function validateDataSchema(sheets: ExcelSheet[], template: string): Promise<void> {
  const templateRules = getTemplateValidationRules(template);
  const errors: string[] = [];

  for (const sheet of sheets) {
    if (!Array.isArray(sheet.data)) continue;

    // Validar estrutura da planilha
    if (sheet.data.length === 0) {
      errors.push(`Planilha '${sheet.name}' está vazia`);
      continue;
    }

    // Verificar se a primeira linha contém cabeçalhos esperados
    const headers = Array.isArray(sheet.data[0]) ? sheet.data[0] : Object.keys(sheet.data[0] || {});
    const requiredColumns = Object.keys(templateRules).filter(col => templateRules[col].required);

    for (const requiredCol of requiredColumns) {
      if (!headers.includes(requiredCol)) {
        errors.push(`Coluna obrigatória '${requiredCol}' não encontrada na planilha '${sheet.name}'`);
      }
    }

    // Validar dados linha por linha
    const dataRows = sheet.data.slice(1); // Pular cabeçalhos
    for (let rowIndex = 0; rowIndex < dataRows.length; rowIndex++) {
      const row = dataRows[rowIndex];
      const rowNumber = rowIndex + 2; // +2 porque começamos do índice 1 e pulamos cabeçalhos

      for (const [column, rules] of Object.entries(templateRules)) {
        const cellValue = Array.isArray(row) ? row[headers.indexOf(column)] : row[column];

        // Validação de campo obrigatório
        if (rules.required && (cellValue === undefined || cellValue === null || cellValue === '')) {
          errors.push(`Linha ${rowNumber}: Campo obrigatório '${column}' está vazio`);
          continue;
        }

        // Pular validação se campo está vazio e não é obrigatório
        if (cellValue === undefined || cellValue === null || cellValue === '') {
          continue;
        }

        // Validação de tipo
        if (rules.type === 'number' && isNaN(Number(cellValue))) {
          errors.push(`Linha ${rowNumber}: Campo '${column}' deve ser um número válido`);
        }

        if (rules.type === 'email' && !isValidEmail(String(cellValue))) {
          errors.push(`Linha ${rowNumber}: Campo '${column}' deve ser um email válido`);
        }

        if (rules.type === 'date' && !isValidDate(cellValue)) {
          errors.push(`Linha ${rowNumber}: Campo '${column}' deve ser uma data válida`);
        }

        if (rules.type === 'url' && !isValidUrl(String(cellValue))) {
          errors.push(`Linha ${rowNumber}: Campo '${column}' deve ser uma URL válida`);
        }

        // Validação de valores permitidos
        if (rules.enum && !rules.enum.includes(cellValue)) {
          errors.push(`Linha ${rowNumber}: Campo '${column}' deve ser um dos valores: ${rules.enum.join(', ')}`);
        }

        // Validação de range numérico
        if (rules.min !== undefined && Number(cellValue) < rules.min) {
          errors.push(`Linha ${rowNumber}: Campo '${column}' deve ser maior ou igual a ${rules.min}`);
        }

        if (rules.max !== undefined && Number(cellValue) > rules.max) {
          errors.push(`Linha ${rowNumber}: Campo '${column}' deve ser menor ou igual a ${rules.max}`);
        }

        // Validação de comprimento de string
        if (rules.minLength !== undefined && String(cellValue).length < rules.minLength) {
          errors.push(`Linha ${rowNumber}: Campo '${column}' deve ter pelo menos ${rules.minLength} caracteres`);
        }

        if (rules.maxLength !== undefined && String(cellValue).length > rules.maxLength) {
          errors.push(`Linha ${rowNumber}: Campo '${column}' deve ter no máximo ${rules.maxLength} caracteres`);
        }

        // Validação de padrão regex
        if (rules.pattern && !new RegExp(rules.pattern).test(String(cellValue))) {
          errors.push(`Linha ${rowNumber}: Campo '${column}' não atende ao padrão esperado`);
        }
      }
    }
  }

  // Se houver erros, lançar exceção com todos os erros
  if (errors.length > 0) {
    throw new Error(`Erros de validação encontrados:\n${errors.join('\n')}`);
  }
}

/**
 * Aplica transformações nos dados baseado no template
 */
async function transformDataByTemplate(sheets: ExcelSheet[], template: string): Promise<void> {
  const transformations = getTemplateTransformations(template);

  for (const sheet of sheets) {
    if (!Array.isArray(sheet.data)) continue;

    sheet.data = sheet.data.map((row: any) => {
      const transformedRow = { ...row };

      for (const [column, transformation] of Object.entries(transformations)) {
        if (transformedRow[column] !== undefined) {
          switch (transformation.type) {
            case 'currency':
              transformedRow[column] = formatCurrency(transformedRow[column]);
              break;
            case 'date':
              transformedRow[column] = formatDate(transformedRow[column]);
              break;
            case 'uppercase':
              transformedRow[column] = String(transformedRow[column]).toUpperCase();
              break;
            case 'lowercase':
              transformedRow[column] = String(transformedRow[column]).toLowerCase();
              break;
          }
        }
      }

      return transformedRow;
    });
  }
}

/**
 * Obtém regras de validação avançadas para um template
 */
function getTemplateValidationRules(template: string): Record<string, any> {
  const rules: Record<string, Record<string, any>> = {
    'financial-expenses': {
      'Data': {
        required: true,
        type: 'date',
        description: 'Data da transação financeira'
      },
      'Valor': {
        required: true,
        type: 'number',
        min: 0,
        description: 'Valor da transação em reais'
      },
      'Tipo': {
        required: true,
        enum: ['Receita', 'Despesa'],
        description: 'Tipo da transação'
      },
      'Categoria': {
        required: false,
        type: 'string',
        maxLength: 50,
        description: 'Categoria da despesa/receita'
      },
      'Descrição': {
        required: false,
        type: 'string',
        maxLength: 200,
        description: 'Descrição detalhada da transação'
      }
    },
    'sales-data': {
      'Data': {
        required: true,
        type: 'date',
        description: 'Data da venda'
      },
      'Produto': {
        required: true,
        type: 'string',
        minLength: 2,
        maxLength: 100,
        description: 'Nome do produto vendido'
      },
      'Quantidade': {
        required: true,
        type: 'number',
        min: 1,
        description: 'Quantidade vendida'
      },
      'Valor Unitário': {
        required: true,
        type: 'number',
        min: 0,
        description: 'Preço unitário do produto'
      },
      'Cliente': {
        required: false,
        type: 'string',
        maxLength: 100,
        description: 'Nome do cliente'
      },
      'Vendedor': {
        required: false,
        type: 'string',
        maxLength: 50,
        description: 'Nome do vendedor responsável'
      }
    },
    'employee-data': {
      'Nome': {
        required: true,
        type: 'string',
        minLength: 2,
        maxLength: 100,
        description: 'Nome completo do funcionário'
      },
      'Email': {
        required: true,
        type: 'email',
        description: 'Email corporativo do funcionário'
      },
      'Data Admissão': {
        required: true,
        type: 'date',
        description: 'Data de admissão na empresa'
      },
      'Cargo': {
        required: true,
        type: 'string',
        minLength: 2,
        maxLength: 50,
        description: 'Cargo do funcionário'
      },
      'Salário': {
        required: false,
        type: 'number',
        min: 0,
        description: 'Salário base do funcionário'
      },
      'Departamento': {
        required: false,
        type: 'string',
        maxLength: 50,
        description: 'Departamento do funcionário'
      }
    },
    'inventory-data': {
      'Código': {
        required: true,
        type: 'string',
        pattern: '^[A-Z0-9]{3,10}$',
        description: 'Código único do produto'
      },
      'Nome': {
        required: true,
        type: 'string',
        minLength: 2,
        maxLength: 100,
        description: 'Nome do produto'
      },
      'Quantidade': {
        required: true,
        type: 'number',
        min: 0,
        description: 'Quantidade em estoque'
      },
      'Preço': {
        required: true,
        type: 'number',
        min: 0,
        description: 'Preço unitário do produto'
      },
      'Categoria': {
        required: false,
        type: 'string',
        maxLength: 50,
        description: 'Categoria do produto'
      }
    },
    'project-tasks': {
      'Tarefa': {
        required: true,
        type: 'string',
        minLength: 5,
        maxLength: 200,
        description: 'Descrição da tarefa'
      },
      'Responsável': {
        required: true,
        type: 'string',
        maxLength: 50,
        description: 'Pessoa responsável pela tarefa'
      },
      'Data Início': {
        required: true,
        type: 'date',
        description: 'Data de início da tarefa'
      },
      'Data Fim': {
        required: false,
        type: 'date',
        description: 'Data prevista para conclusão'
      },
      'Status': {
        required: true,
        enum: ['Não Iniciado', 'Em Andamento', 'Concluído', 'Cancelado'],
        description: 'Status atual da tarefa'
      },
      'Prioridade': {
        required: false,
        enum: ['Baixa', 'Média', 'Alta', 'Crítica'],
        description: 'Prioridade da tarefa'
      }
    }
  };

  return rules[template] || {};
}

/**
 * Obtém transformações para um template
 */
function getTemplateTransformations(template: string): Record<string, any> {
  const transformations: Record<string, Record<string, any>> = {
    'financial-expenses': {
      'Valor': { type: 'currency' },
      'Data': { type: 'date' }
    },
    'sales-data': {
      'Valor Unitário': { type: 'currency' },
      'Total': { type: 'currency' },
      'Data': { type: 'date' }
    },
    'employee-data': {
      'Nome': { type: 'uppercase' },
      'Email': { type: 'lowercase' },
      'Data Admissão': { type: 'date' }
    }
  };

  return transformations[template] || {};
}

/**
 * Valida se é um email válido
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Valida se uma data é válida
 */
function isValidDate(value: unknown): boolean {
  if (!value) return false;

  // Se já é uma data
  if (value instanceof Date) {
    return !isNaN(value.getTime());
  }

  // Tentar converter string para data
  const dateStr = String(value);
  const date = new Date(dateStr);

  // Verificar se a data é válida
  if (isNaN(date.getTime())) {
    // Tentar formatos brasileiros (DD/MM/YYYY)
    const brDateRegex = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
    const match = dateStr.match(brDateRegex);
    if (match) {
      const [, day, month, year] = match;
      if (day && month && year) {
        const brDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
        return !isNaN(brDate.getTime());
      }
    }
    return false;
  }

  return true;
}

/**
 * Valida se uma URL é válida
 */
function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * Formata valor como moeda
 */
function formatCurrency(value: any): string {
  const num = Number(value);
  if (isNaN(num)) return String(value);
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(num);
}

/**
 * Formata data
 */
function formatDate(value: any): string {
  try {
    const date = new Date(value);
    if (isNaN(date.getTime())) return String(value);
    return date.toLocaleDateString('pt-BR');
  } catch {
    return String(value);
  }
}
