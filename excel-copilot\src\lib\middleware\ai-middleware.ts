/**
 * Middleware específico para APIs de IA
 * Aplica rate limiting, autenticação e logging automaticamente
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';

import { logger } from '@/lib/logger';
import { AIRateLimiter } from '@/lib/security/ai-rate-limiter';

export interface AIMiddlewareOptions {
  operationType: 'chat' | 'analysis' | 'generation' | 'translation';
  requireAuth?: boolean;
  estimateTokens?: (request: NextRequest) => Promise<number>;
  extractWorkbookId?: (request: NextRequest) => Promise<string | undefined>;
}

export interface AIMiddlewareContext {
  userId: string;
  userTier: 'free' | 'pro';
  workbookId: string | undefined;
  estimatedTokens: number;
  isAuthenticated: boolean;
}

/**
 * Middleware para APIs de IA com rate limiting e autenticação
 */
export function withAIMiddleware(
  options: AIMiddlewareOptions,
  handler: (req: NextRequest, context: AIMiddlewareContext) => Promise<NextResponse>
) {
  return async (req: NextRequest): Promise<NextResponse> => {
    const startTime = Date.now();
    
    try {
      // 1. Verificar autenticação se necessário
      let userId = 'anonymous';
      let userTier: 'free' | 'pro' = 'free';
      let isAuthenticated = false;

      if (options.requireAuth !== false) {
        const session = await getServerSession();
        
        if (!session?.user) {
          return NextResponse.json(
            { error: 'Não autorizado. Faça login para continuar.' },
            { status: 401 }
          );
        }

        userId = session.user.id;
        isAuthenticated = true;
        
        // TODO: Determinar tier do usuário baseado na subscription
        // const subscription = await getUserSubscription(userId);
        // userTier = subscription?.plan === 'pro' ? 'pro' : 'free';
      }

      // 2. Estimar tokens da requisição
      let estimatedTokens = 1000; // Valor padrão
      if (options.estimateTokens) {
        try {
          estimatedTokens = await options.estimateTokens(req);
        } catch (error) {
          logger.warn('Erro ao estimar tokens:', error);
        }
      } else {
        // Estimativa simples baseada no tamanho do body
        try {
          const body = await req.clone().text();
          estimatedTokens = Math.ceil(body.length / 4);
        } catch {
          // Usar valor padrão se não conseguir ler o body
        }
      }

      // 3. Extrair workbook ID se fornecido
      let workbookId: string | undefined;
      if (options.extractWorkbookId) {
        try {
          workbookId = await options.extractWorkbookId(req);
        } catch (error) {
          logger.warn('Erro ao extrair workbook ID:', error);
        }
      }

      // 4. Aplicar rate limiting específico para IA
      const aiRateLimiter = AIRateLimiter.getInstance();
      const rateLimitResult = await aiRateLimiter.checkLimit(
        userId,
        options.operationType,
        estimatedTokens,
        userTier
      );

      if (!rateLimitResult.allowed) {
        // Log da violação
        logger.warn('Rate limit de IA excedido:', {
          userId,
          operationType: options.operationType,
          reason: rateLimitResult.reason,
          estimatedTokens,
          userTier,
        });

        return NextResponse.json(
          {
            error: rateLimitResult.reason || 'Limite de requisições de IA excedido.',
            retryAfter: rateLimitResult.retryAfter || 60,
            remaining: rateLimitResult.remaining,
            resetTime: rateLimitResult.resetTime,
            details: {
              operationType: options.operationType,
              userTier,
              estimatedTokens,
            },
          },
          { 
            status: 429,
            headers: {
              'Retry-After': String(rateLimitResult.retryAfter || 60),
              'X-RateLimit-Remaining-Minute': String(rateLimitResult.remaining.minute),
              'X-RateLimit-Remaining-Hour': String(rateLimitResult.remaining.hour),
              'X-RateLimit-Remaining-Day': String(rateLimitResult.remaining.day),
              'X-RateLimit-Reset-Minute': String(rateLimitResult.resetTime.minute),
              'X-RateLimit-Reset-Hour': String(rateLimitResult.resetTime.hour),
              'X-RateLimit-Reset-Day': String(rateLimitResult.resetTime.day),
            }
          }
        );
      }

      // 5. Criar contexto para o handler
      const context: AIMiddlewareContext = {
        userId,
        userTier,
        workbookId,
        estimatedTokens,
        isAuthenticated,
      };

      // 6. Log da requisição
      logger.info('Requisição de IA autorizada:', {
        userId,
        operationType: options.operationType,
        estimatedTokens,
        userTier,
        workbookId,
        remaining: rateLimitResult.remaining,
      });

      // 7. Executar handler
      const response = await handler(req, context);

      // 8. Log da resposta
      const responseTime = Date.now() - startTime;
      logger.info('Resposta de IA enviada:', {
        userId,
        operationType: options.operationType,
        responseTime,
        status: response.status,
      });

      // 9. Adicionar headers de rate limiting na resposta
      response.headers.set('X-RateLimit-Remaining-Minute', String(rateLimitResult.remaining.minute));
      response.headers.set('X-RateLimit-Remaining-Hour', String(rateLimitResult.remaining.hour));
      response.headers.set('X-RateLimit-Remaining-Day', String(rateLimitResult.remaining.day));

      return response;

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      logger.error('Erro no middleware de IA:', {
        error: error instanceof Error ? error.message : String(error),
        operationType: options.operationType,
        responseTime,
      });

      return NextResponse.json(
        {
          error: 'Erro interno do servidor.',
          details: process.env.NODE_ENV === 'development' ? String(error) : undefined,
        },
        { status: 500 }
      );
    }
  };
}

/**
 * Funções auxiliares para extrair informações da requisição
 */
export const aiMiddlewareHelpers = {
  /**
   * Extrai workbook ID do body da requisição
   */
  extractWorkbookIdFromBody: async (req: NextRequest): Promise<string | undefined> => {
    try {
      const body = await req.clone().json();
      return body.workbookId || body.context?.workbookId;
    } catch {
      return undefined;
    }
  },

  /**
   * Extrai workbook ID dos parâmetros da URL
   */
  extractWorkbookIdFromParams: async (req: NextRequest): Promise<string | undefined> => {
    try {
      const url = new URL(req.url);
      const pathParts = url.pathname.split('/');
      const workbookIndex = pathParts.findIndex(part => part === 'workbooks');
      
      if (workbookIndex !== -1 && pathParts[workbookIndex + 1]) {
        return pathParts[workbookIndex + 1];
      }
      
      return url.searchParams.get('workbookId') || undefined;
    } catch {
      return undefined;
    }
  },

  /**
   * Estima tokens baseado no conteúdo da mensagem
   */
  estimateTokensFromMessage: async (req: NextRequest): Promise<number> => {
    try {
      const body = await req.clone().json();
      const message = body.message || body.prompt || body.content || '';
      const context = body.context || '';
      
      const totalText = message + context;
      return Math.ceil(totalText.length / 4); // ~4 caracteres por token
    } catch {
      return 1000; // Valor padrão
    }
  },

  /**
   * Estima tokens baseado no tamanho total do body
   */
  estimateTokensFromBody: async (req: NextRequest): Promise<number> => {
    try {
      const body = await req.clone().text();
      return Math.ceil(body.length / 4);
    } catch {
      return 1000; // Valor padrão
    }
  },
};

/**
 * Middleware pré-configurado para chat
 */
export const withChatMiddleware = (
  handler: (req: NextRequest, context: AIMiddlewareContext) => Promise<NextResponse>
) => {
  return withAIMiddleware(
    {
      operationType: 'chat',
      requireAuth: true,
      estimateTokens: aiMiddlewareHelpers.estimateTokensFromMessage,
      extractWorkbookId: aiMiddlewareHelpers.extractWorkbookIdFromBody,
    },
    handler
  );
};

/**
 * Middleware pré-configurado para análise
 */
export const withAnalysisMiddleware = (
  handler: (req: NextRequest, context: AIMiddlewareContext) => Promise<NextResponse>
) => {
  return withAIMiddleware(
    {
      operationType: 'analysis',
      requireAuth: true,
      estimateTokens: aiMiddlewareHelpers.estimateTokensFromBody,
      extractWorkbookId: aiMiddlewareHelpers.extractWorkbookIdFromParams,
    },
    handler
  );
};
