'use client';

import { useState } from 'react';
import { Shield, Key, Copy, Check, Mail, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface TwoFactorSetupProps {
  isEnabled: boolean;
  isRequired: boolean;
  onStatusChange?: (enabled: boolean) => void;
}

export function TwoFactorSetup({ isEnabled, isRequired, onStatusChange }: TwoFactorSetupProps) {
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<'setup' | 'verify' | 'complete'>('setup');
  const [secret, setSecret] = useState<string>('');
  const [backupCodes, setBackupCodes] = useState<string[]>([]);
  const [verificationCode, setVerificationCode] = useState('');
  const [copiedCodes, setCopiedCodes] = useState(false);

  const handleEnable2FA = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/auth/two-factor', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'enable' }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao habilitar 2FA');
      }

      setSecret(data.secret);
      setBackupCodes(data.backupCodes);
      setStep('verify');
      toast.success('2FA configurado! Agora verifique com um código.');
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Erro ao habilitar 2FA');
    } finally {
      setLoading(false);
    }
  };

  const handleSendCode = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/auth/two-factor', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'send-code' }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao enviar código');
      }

      toast.success('Código enviado por email!');
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Erro ao enviar código');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyCode = async () => {
    if (!verificationCode) {
      toast.error('Digite o código de verificação');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/auth/two-factor', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'verify-code', code: verificationCode }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Código inválido');
      }

      setStep('complete');
      toast.success('2FA verificado com sucesso!');
      onStatusChange?.(true);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Código inválido');
    } finally {
      setLoading(false);
    }
  };

  const handleDisable2FA = async () => {
    if (isRequired) {
      toast.error('Usuários administrativos não podem desabilitar 2FA');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/auth/two-factor', {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao desabilitar 2FA');
      }

      toast.success('2FA desabilitado com sucesso');
      onStatusChange?.(false);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Erro ao desabilitar 2FA');
    } finally {
      setLoading(false);
    }
  };

  const copyBackupCodes = () => {
    const codesText = backupCodes.join('\n');
    navigator.clipboard.writeText(codesText);
    setCopiedCodes(true);
    toast.success('Códigos de backup copiados!');
    setTimeout(() => setCopiedCodes(false), 2000);
  };

  if (isEnabled) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-green-500" />
            Autenticação de Dois Fatores
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              Ativado
            </Badge>
          </CardTitle>
          <CardDescription>
            Sua conta está protegida com autenticação de dois fatores.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {isRequired && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Como usuário administrativo, o 2FA é obrigatório e não pode ser desabilitado.
              </AlertDescription>
            </Alert>
          )}
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleSendCode}
              disabled={loading}
              className="flex items-center gap-2"
            >
              <Mail className="h-4 w-4" />
              Enviar Código de Teste
            </Button>
            
            {!isRequired && (
              <Button
                variant="destructive"
                onClick={handleDisable2FA}
                disabled={loading}
              >
                Desabilitar 2FA
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Key className="h-5 w-5" />
          Configurar Autenticação de Dois Fatores
          {isRequired && (
            <Badge variant="destructive">Obrigatório</Badge>
          )}
        </CardTitle>
        <CardDescription>
          Adicione uma camada extra de segurança à sua conta.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {isRequired && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Como usuário administrativo, você deve configurar a autenticação de dois fatores.
            </AlertDescription>
          </Alert>
        )}

        {step === 'setup' && (
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              A autenticação de dois fatores adiciona uma camada extra de segurança à sua conta.
              Você receberá códigos por email quando necessário.
            </p>
            <Button onClick={handleEnable2FA} disabled={loading}>
              {loading ? 'Configurando...' : 'Habilitar 2FA'}
            </Button>
          </div>
        )}

        {step === 'verify' && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="verification-code">Código de Verificação</Label>
              <Input
                id="verification-code"
                type="text"
                placeholder="Digite o código de 6 dígitos"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
                maxLength={6}
              />
            </div>
            
            <div className="flex gap-2">
              <Button onClick={handleSendCode} variant="outline" disabled={loading}>
                Enviar Código por Email
              </Button>
              <Button onClick={handleVerifyCode} disabled={loading || !verificationCode}>
                Verificar Código
              </Button>
            </div>

            {backupCodes.length > 0 && (
              <div className="space-y-2">
                <Label>Códigos de Backup</Label>
                <div className="p-3 bg-muted rounded-md">
                  <div className="grid grid-cols-2 gap-1 text-sm font-mono">
                    {backupCodes.map((code, index) => (
                      <div key={index}>{code}</div>
                    ))}
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={copyBackupCodes}
                  className="flex items-center gap-2"
                >
                  {copiedCodes ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  {copiedCodes ? 'Copiado!' : 'Copiar Códigos'}
                </Button>
                <p className="text-xs text-muted-foreground">
                  Guarde estes códigos em local seguro. Eles podem ser usados se você perder acesso ao email.
                </p>
              </div>
            )}
          </div>
        )}

        {step === 'complete' && (
          <div className="space-y-4">
            <div className="text-center">
              <Shield className="h-12 w-12 text-green-500 mx-auto mb-2" />
              <h3 className="text-lg font-semibold">2FA Configurado com Sucesso!</h3>
              <p className="text-sm text-muted-foreground">
                Sua conta agora está protegida com autenticação de dois fatores.
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
