/**
 * Otimizador de performance para operações Excel
 * Implementa chunking, lazy loading, cache e otimizações específicas
 */

import { logger } from '@/lib/logger';

export interface PerformanceMetrics {
  processingTime: number;
  memoryUsage: number;
  chunksProcessed: number;
  cacheHits: number;
  cacheMisses: number;
  optimizationsApplied: string[];
}

export interface OptimizationOptions {
  chunkSize?: number;
  enableCache?: boolean;
  enableLazyLoading?: boolean;
  enableVirtualization?: boolean;
  maxMemoryUsage?: number; // em MB
  enableProgressCallback?: boolean;
}

export interface ProcessingResult<T> {
  data: T;
  metrics: PerformanceMetrics;
  warnings: string[];
}

export class ExcelPerformanceOptimizer {
  private cache = new Map<string, { data: unknown; timestamp: number; size: number }>();
  private readonly cacheMaxSize = 100 * 1024 * 1024; // 100MB
  private readonly cacheMaxAge = 30 * 60 * 1000; // 30 minutos
  private currentMemoryUsage = 0;

  private readonly defaultOptions: Required<OptimizationOptions> = {
    chunkSize: 1000, // 1000 linhas por chunk
    enableCache: true,
    enableLazyLoading: true,
    enableVirtualization: true,
    maxMemoryUsage: 512, // 512MB
    enableProgressCallback: true,
  };

  /**
   * Processa dados Excel com otimizações de performance
   */
  async processLargeDataset<T>(
    data: unknown[][],
    processor: (chunk: unknown[][], chunkIndex: number, totalChunks: number) => Promise<T>,
    options: OptimizationOptions = {},
    onProgress?: (progress: number, metrics: Partial<PerformanceMetrics>) => void
  ): Promise<ProcessingResult<T[]>> {
    const startTime = performance.now();
    const opts = { ...this.defaultOptions, ...options };
    const warnings: string[] = [];
    const optimizationsApplied: string[] = [];

    // Verificar tamanho dos dados
    const dataSize = this.estimateDataSize(data);
    if (dataSize > opts.maxMemoryUsage * 1024 * 1024) {
      warnings.push(`Dataset muito grande (${Math.round(dataSize / 1024 / 1024)}MB). Considere dividir em partes menores.`);
    }

    // Aplicar otimizações baseadas no tamanho
    const optimizedOptions = this.analyzeAndOptimize(data, opts);
    optimizationsApplied.push(...optimizedOptions.applied);

    // Dividir dados em chunks
    const chunks = this.chunkData(data, optimizedOptions.chunkSize);
    const results: T[] = [];
    let cacheHits = 0;
    let cacheMisses = 0;

    logger.info('Iniciando processamento otimizado', {
      totalRows: data.length,
      totalChunks: chunks.length,
      chunkSize: optimizedOptions.chunkSize,
      estimatedSize: Math.round(dataSize / 1024 / 1024) + 'MB',
      optimizations: optimizationsApplied,
    });

    // Processar chunks
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      if (!chunk) continue;

      const chunkKey = this.generateChunkKey(chunk);

      // Verificar cache se habilitado
      if (opts.enableCache) {
        const cached = this.getFromCache(chunkKey);
        if (cached) {
          results.push(cached as T);
          cacheHits++;
          
          if (onProgress) {
            onProgress((i + 1) / chunks.length * 100, {
              processingTime: performance.now() - startTime,
              chunksProcessed: i + 1,
              cacheHits,
              cacheMisses,
            });
          }
          continue;
        }
        cacheMisses++;
      }

      // Verificar uso de memória
      if (this.currentMemoryUsage > opts.maxMemoryUsage * 1024 * 1024 * 0.8) {
        await this.cleanupMemory();
        warnings.push('Limpeza de memória executada durante processamento');
      }

      try {
        // Processar chunk
        const result = await processor(chunk, i, chunks.length);
        results.push(result);

        // Salvar no cache se habilitado
        if (opts.enableCache) {
          this.saveToCache(chunkKey, result);
        }

        // Callback de progresso
        if (onProgress) {
          onProgress((i + 1) / chunks.length * 100, {
            processingTime: performance.now() - startTime,
            chunksProcessed: i + 1,
            cacheHits,
            cacheMisses,
          });
        }

        // Yield para não bloquear a UI
        if (i % 10 === 0) {
          await this.yield();
        }
      } catch (error) {
        logger.error('Erro no processamento do chunk', {
          chunkIndex: i,
          chunkSize: chunk?.length || 0,
          error: error instanceof Error ? error.message : 'Erro desconhecido',
        });
        throw error;
      }
    }

    const processingTime = performance.now() - startTime;

    const metrics: PerformanceMetrics = {
      processingTime,
      memoryUsage: this.currentMemoryUsage,
      chunksProcessed: chunks.length,
      cacheHits,
      cacheMisses,
      optimizationsApplied,
    };

    logger.info('Processamento concluído', {
      totalTime: Math.round(processingTime),
      chunksProcessed: chunks.length,
      cacheEfficiency: cacheHits / (cacheHits + cacheMisses) * 100,
      memoryUsage: Math.round(this.currentMemoryUsage / 1024 / 1024) + 'MB',
    });

    return {
      data: results,
      metrics,
      warnings,
    };
  }

  /**
   * Implementa lazy loading para dados grandes
   */
  createLazyLoader<T>(
    data: unknown[][],
    pageSize: number = 100
  ): {
    loadPage: (page: number) => Promise<unknown[][]>;
    getTotalPages: () => number;
    getPageInfo: (page: number) => { start: number; end: number; size: number };
  } {
    const totalPages = Math.ceil(data.length / pageSize);

    return {
      loadPage: async (page: number) => {
        if (page < 0 || page >= totalPages) {
          throw new Error(`Página ${page} inválida. Total de páginas: ${totalPages}`);
        }

        const start = page * pageSize;
        const end = Math.min(start + pageSize, data.length);
        
        // Simular delay de carregamento para grandes datasets
        if (data.length > 10000) {
          await this.yield();
        }

        return data.slice(start, end);
      },

      getTotalPages: () => totalPages,

      getPageInfo: (page: number) => {
        const start = page * pageSize;
        const end = Math.min(start + pageSize, data.length);
        return { start, end, size: end - start };
      },
    };
  }

  /**
   * Otimiza operações de busca em grandes datasets
   */
  createSearchIndex(data: unknown[][], searchColumns: number[]): Map<string, number[]> {
    const index = new Map<string, number[]>();

    for (let rowIndex = 0; rowIndex < data.length; rowIndex++) {
      const row = data[rowIndex];
      
      for (const colIndex of searchColumns) {
        if (row && colIndex < row.length) {
          const value = String(row[colIndex]).toLowerCase();
          
          // Indexar palavras individuais
          const words = value.split(/\s+/);
          for (const word of words) {
            if (word.length > 2) { // Ignorar palavras muito curtas
              if (!index.has(word)) {
                index.set(word, []);
              }
              index.get(word)!.push(rowIndex);
            }
          }
        }
      }
    }

    return index;
  }

  /**
   * Busca otimizada usando índice
   */
  searchWithIndex(
    searchIndex: Map<string, number[]>,
    query: string,
    data: unknown[][]
  ): { rowIndex: number; row: unknown[] }[] {
    const queryWords = query.toLowerCase().split(/\s+/);
    let matchingRows = new Set<number>();

    // Primeira palavra define o conjunto inicial
    if (queryWords.length > 0 && queryWords[0]) {
      const firstWordMatches = searchIndex.get(queryWords[0]) || [];
      matchingRows = new Set(firstWordMatches);

      // Intersecção com outras palavras
      for (let i = 1; i < queryWords.length; i++) {
        const word = queryWords[i];
        if (word) {
          const wordMatches = new Set(searchIndex.get(word) || []);
          matchingRows = new Set([...matchingRows].filter(x => wordMatches.has(x)));
        }
      }
    }

    return Array.from(matchingRows)
      .slice(0, 1000) // Limitar resultados
      .map(rowIndex => ({ rowIndex, row: data[rowIndex] || [] }));
  }

  /**
   * Analisa dados e sugere otimizações
   */
  private analyzeAndOptimize(
    data: unknown[][],
    options: Required<OptimizationOptions>
  ): Required<OptimizationOptions> & { applied: string[] } {
    const applied: string[] = [];
    const optimized = { ...options };

    const rowCount = data.length;
    const avgRowSize = data.length > 0 ? this.estimateDataSize(data) / data.length : 0;

    // Ajustar chunk size baseado no tamanho dos dados
    if (rowCount > 50000) {
      optimized.chunkSize = Math.min(500, optimized.chunkSize);
      applied.push('Chunk size reduzido para datasets grandes');
    } else if (rowCount > 10000) {
      optimized.chunkSize = Math.min(1000, optimized.chunkSize);
      applied.push('Chunk size otimizado para datasets médios');
    }

    // Ajustar baseado no tamanho médio das linhas
    if (avgRowSize > 1024) { // Linhas grandes
      optimized.chunkSize = Math.min(optimized.chunkSize, 200);
      applied.push('Chunk size reduzido para linhas grandes');
    }

    // Habilitar cache para datasets que se beneficiam
    if (rowCount > 1000 && !optimized.enableCache) {
      optimized.enableCache = true;
      applied.push('Cache habilitado automaticamente');
    }

    // Habilitar virtualização para datasets muito grandes
    if (rowCount > 100000 && !optimized.enableVirtualization) {
      optimized.enableVirtualization = true;
      applied.push('Virtualização habilitada automaticamente');
    }

    return { ...optimized, applied };
  }

  /**
   * Divide dados em chunks
   */
  private chunkData(data: unknown[][], chunkSize: number): unknown[][][] {
    const chunks: unknown[][][] = [];
    
    for (let i = 0; i < data.length; i += chunkSize) {
      chunks.push(data.slice(i, i + chunkSize));
    }

    return chunks;
  }

  /**
   * Estima tamanho dos dados em bytes
   */
  private estimateDataSize(data: unknown[][]): number {
    if (data.length === 0) return 0;

    // Estimar baseado em uma amostra
    const sampleSize = Math.min(100, data.length);
    let totalSize = 0;

    for (let i = 0; i < sampleSize; i++) {
      const row = data[i];
      if (row) {
        for (const cell of row) {
        if (typeof cell === 'string') {
          totalSize += cell.length * 2; // UTF-16
        } else if (typeof cell === 'number') {
          totalSize += 8; // 64-bit number
        } else if (cell instanceof Date) {
          totalSize += 8;
        } else {
          totalSize += JSON.stringify(cell).length * 2;
        }
      }
      }
    }

    // Extrapolar para todo o dataset
    return (totalSize / sampleSize) * data.length;
  }

  /**
   * Gera chave única para um chunk
   */
  private generateChunkKey(chunk: unknown[][]): string {
    // Hash simples baseado no conteúdo
    const content = JSON.stringify(chunk.slice(0, 3)); // Primeiras 3 linhas
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 32-bit integer
    }
    return `chunk_${Math.abs(hash)}_${chunk.length}`;
  }

  /**
   * Obtém dados do cache
   */
  private getFromCache(key: string): unknown | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    // Verificar se não expirou
    if (Date.now() - cached.timestamp > this.cacheMaxAge) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  /**
   * Salva dados no cache
   */
  private saveToCache(key: string, data: unknown): void {
    const size = JSON.stringify(data).length * 2; // Estimativa UTF-16
    
    // Verificar se cabe no cache
    if (size > this.cacheMaxSize * 0.1) return; // Não cachear itens muito grandes

    // Limpar cache se necessário
    while (this.currentMemoryUsage + size > this.cacheMaxSize && this.cache.size > 0) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) {
        const oldItem = this.cache.get(oldestKey);
        if (oldItem) {
          this.currentMemoryUsage -= oldItem.size;
        }
        this.cache.delete(oldestKey);
      } else {
        break;
      }
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      size,
    });

    this.currentMemoryUsage += size;
  }

  /**
   * Limpa memória removendo itens antigos do cache
   */
  private async cleanupMemory(): Promise<void> {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > this.cacheMaxAge * 0.5) { // Remover itens com mais de 15 min
        this.currentMemoryUsage -= item.size;
        this.cache.delete(key);
        cleaned++;
      }
    }

    // Forçar garbage collection se disponível
    if (typeof global !== 'undefined' && global.gc) {
      global.gc();
    }

    logger.debug('Limpeza de memória executada', {
      itemsRemoved: cleaned,
      currentMemoryUsage: Math.round(this.currentMemoryUsage / 1024 / 1024) + 'MB',
      cacheSize: this.cache.size,
    });
  }

  /**
   * Yield para não bloquear a UI
   */
  private async yield(): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, 0));
  }

  /**
   * Obtém estatísticas do cache
   */
  getCacheStats(): {
    size: number;
    memoryUsage: number;
    hitRate: number;
    oldestItem: number;
  } {
    let oldestTimestamp = Date.now();
    let totalHits = 0;
    let totalRequests = 0;

    for (const item of this.cache.values()) {
      oldestTimestamp = Math.min(oldestTimestamp, item.timestamp);
    }

    return {
      size: this.cache.size,
      memoryUsage: this.currentMemoryUsage,
      hitRate: totalRequests > 0 ? totalHits / totalRequests : 0,
      oldestItem: Date.now() - oldestTimestamp,
    };
  }
}

// Instância singleton do otimizador de performance
export const performanceOptimizer = new ExcelPerformanceOptimizer();
