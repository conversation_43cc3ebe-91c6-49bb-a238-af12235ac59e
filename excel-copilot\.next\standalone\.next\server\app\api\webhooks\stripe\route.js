"use strict";(()=>{var e={};e.id=798,e.ids=[798],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},32081:e=>{e.exports=require("child_process")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},21528:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>y,patchFetch:()=>R,requestAsyncStorage:()=>k,routeModule:()=>E,serverHooks:()=>_,staticGenerationAsyncStorage:()=>S});var a={};t.r(a),t.d(a,{POST:()=>g,dynamic:()=>l,fetchCache:()=>p,runtime:()=>m});var n=t(49303),o=t(88716),i=t(60670),s=t(87070),c=t(43895),u=t(46029),d=t(63841);let l="force-dynamic",p="force-no-store",m="nodejs";async function f(e){let r=[],t=e.body?.getReader();if(!t)throw Error("Request body is empty");let a=!1;for(;!a;){let{done:e,value:n}=await t.read();e?a=!0:r.push(n)}return Buffer.concat(r)}async function g(e){let r=Date.now(),t=e.ip||e.headers.get("x-forwarded-for")||"unknown",a=e.headers.get("user-agent")||"unknown";try{let n;let o=await f(e),i=e.headers.get("stripe-signature");if(!o||0===o.length)return c.kg.warn("[WEBHOOK_SECURITY] Tentativa de acesso com corpo vazio",{ip:t,userAgent:a,timestamp:new Date().toISOString()}),s.NextResponse.json({error:"Corpo da requisi\xe7\xe3o vazio"},{status:400});if(!i)return c.kg.warn("[WEBHOOK_SECURITY] Tentativa de acesso sem assinatura",{ip:t,userAgent:a,bodyLength:o.length,timestamp:new Date().toISOString()}),s.NextResponse.json({error:"No signature header"},{status:400});let d=u.Cf;if(!d)return c.kg.error("[WEBHOOK_ERROR] Stripe webhook secret n\xe3o configurado",{ip:t,timestamp:new Date().toISOString()}),s.NextResponse.json({error:"Webhook n\xe3o configurado"},{status:500});try{if(!u.Ag)throw Error("Stripe n\xe3o est\xe1 configurado");n=u.Ag.webhooks.constructEvent(o,i,d),c.kg.info("[WEBHOOK_SUCCESS] Assinatura validada com sucesso",{eventType:n.type,eventId:n.id,ip:t,timestamp:new Date().toISOString()})}catch(n){let e=n instanceof Error?n.message:"Unknown error";return c.kg.error("[WEBHOOK_SECURITY] Tentativa de bypass detectada - Assinatura inv\xe1lida",{error:e,ip:t,userAgent:a,signature:i.substring(0,20)+"...",bodyLength:o.length,timestamp:new Date().toISOString(),duration:Date.now()-r}),s.NextResponse.json({error:`Webhook Error: ${e}`},{status:400})}let l=new Date(1e3*n.created),p=new Date,m=p.getTime()-l.getTime();if(m>3e5)return c.kg.warn("[WEBHOOK_SECURITY] Evento muito antigo detectado - poss\xedvel replay attack",{eventId:n.id,eventType:n.type,eventTime:l.toISOString(),currentTime:p.toISOString(),ageDiff:m,ip:t,userAgent:a}),s.NextResponse.json({error:"Evento expirado"},{status:400});switch(n.type){case"checkout.session.completed":{let e=n.data.object;await w(e),c.kg.info("Checkout completed - cache will auto-invalidate");break}case"invoice.paid":{let e=n.data.object;await h(e);break}case"customer.subscription.updated":{let e=n.data.object;await v(e),c.kg.info("Subscription updated - cache will auto-invalidate");break}case"customer.subscription.deleted":{let e=n.data.object;await b(e),c.kg.info("Subscription deleted - cache will auto-invalidate");break}default:c.kg.info(`Evento n\xe3o processado: ${n.type}`)}return s.NextResponse.json({received:!0})}catch(e){return c.kg.error("[WEBHOOK_ERROR]",e),s.NextResponse.json({error:"Erro no webhook"},{status:500})}}async function w(e){let r=e.metadata?.userId,t=e.metadata?.plan;if(!r||!t){c.kg.error("Checkout missing userId or plan:",e.id);return}let a=e.subscription;if(!a){c.kg.error("Checkout missing subscription ID:",e.id);return}try{if(!u.Ag){c.kg.error("Stripe n\xe3o est\xe1 configurado para processar checkout");return}let n=await u.Ag.subscriptions.retrieve(a),o=(0,u.DI)(n.status||"canceled");await d.prisma.subscription.upsert({where:{stripeSubscriptionId:a},create:{userId:r,plan:t,status:o,stripeCustomerId:e.customer,stripeSubscriptionId:a,stripePriceId:n.items.data[0]?.price?.id||"",currentPeriodStart:new Date(n.current_period_start?1e3*n.current_period_start:Date.now()),currentPeriodEnd:new Date(n.current_period_end?1e3*n.current_period_end:Date.now()),apiCallsLimit:u.cb[t]||u.cb[u.Xf.FREE]||50,apiCallsUsed:0},update:{status:o,plan:t,currentPeriodStart:new Date(n.current_period_start?1e3*n.current_period_start:Date.now()),currentPeriodEnd:new Date(n.current_period_end?1e3*n.current_period_end:Date.now()),apiCallsLimit:u.cb[t]||u.cb[u.Xf.FREE]||50}}),c.kg.info(`Assinatura criada/atualizada: ${a} para usu\xe1rio ${r}`)}catch(e){c.kg.error("Erro ao processar checkout:",e)}}async function h(e){let r=e.subscription;if(!r){c.kg.error("Fatura sem ID de assinatura:",e.id);return}try{let t=await d.prisma.subscription.findUnique({where:{stripeSubscriptionId:r}});if(!t){c.kg.error(`Assinatura n\xe3o encontrada: ${r}`);return}await d.prisma.payment.create({data:{amount:e.amount_paid,currency:e.currency,stripePaymentId:e.payment_intent||null,stripeInvoiceId:e.id||null,status:"succeeded",subscriptionId:t.id,metadata:JSON.stringify({billingReason:e.billing_reason,invoiceNumber:e.number})}}),c.kg.info(`Pagamento registrado para assinatura ${r}`)}catch(e){c.kg.error("Erro ao processar pagamento de fatura:",e)}}async function v(e){let r=e.id,t=(0,u.DI)(e.status);try{let a=await d.prisma.subscription.findUnique({where:{stripeSubscriptionId:r}});if(!a){c.kg.error(`Assinatura n\xe3o encontrada no banco: ${r}`);return}let n=e.items.data[0]?.price?.id;if(!n){c.kg.error(`Assinatura sem price ID: ${r}`);return}await d.prisma.subscription.update({where:{id:a.id},data:{status:t,stripePriceId:n,currentPeriodStart:new Date(e.current_period_start?1e3*e.current_period_start:Date.now()),currentPeriodEnd:new Date(e.current_period_end?1e3*e.current_period_end:Date.now()),cancelAtPeriodEnd:e.cancel_at_period_end}}),c.kg.info(`Assinatura atualizada: ${r}`)}catch(e){c.kg.error("Erro ao atualizar assinatura:",e)}}async function b(e){let r=e.id;try{let e=await d.prisma.subscription.findUnique({where:{stripeSubscriptionId:r}});if(!e){c.kg.error(`Assinatura n\xe3o encontrada no banco: ${r}`);return}await d.prisma.subscription.update({where:{id:e.id},data:{status:"canceled",cancelAtPeriodEnd:!1}}),c.kg.info(`Assinatura cancelada: ${r}`)}catch(e){c.kg.error("Erro ao cancelar assinatura:",e)}}let E=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/webhooks/stripe/route",pathname:"/api/webhooks/stripe",filename:"route",bundlePath:"app/api/webhooks/stripe/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\webhooks\\stripe\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:k,staticGenerationAsyncStorage:S,serverHooks:_}=E,y="/api/webhooks/stripe/route";function R(){return(0,i.patchFetch)({serverHooks:_,staticGenerationAsyncStorage:S})}},43895:(e,r,t)=>{let a;t.d(r,{kg:()=>d});var n=t(99557),o=t.n(n);function i(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function s(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let r=["name","message","stack"],t={};return Object.keys(e).forEach(a=>{r.includes(a)||(t[a]=e[a])}),{normalizedError:e,extractedMetadata:t}}return"object"==typeof e&&null!==e?{normalizedError:i(e),extractedMetadata:e}:{normalizedError:i(e),extractedMetadata:{}}}function c(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let u={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:o().stdSerializers.err,error:o().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:o().stdSerializers.err,error:o().stdSerializers.err}}};try{let e=u.production;a=o()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),a=o()({level:"info",formatters:{level:e=>({level:e})}})}let d={trace:(e,r)=>{a.trace(r||{},e)},debug:(e,r)=>{a.debug(r||{},e)},info:(e,r)=>{a.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:t}=s(r);a.warn(t,e)}else a.warn(c(r)||{},e)},error:(e,r,t)=>{let{normalizedError:n,extractedMetadata:o}=s(r),i={...t||{},...o,...n&&{error:{message:n.message,stack:n.stack,name:n.name}}};a.error(i,e)},fatal:(e,r,t)=>{let{normalizedError:n,extractedMetadata:o}=s(r),i={...t||{},...o,...n&&{error:{message:n.message,stack:n.stack,name:n.name}}};a.fatal(i,e)},createChild:e=>{let r=a.child(e);return{trace:(e,t)=>{r.trace(t||{},e)},debug:(e,t)=>{r.debug(t||{},e)},info:(e,t)=>{r.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:a}=s(t);r.warn(a,e)}else r.warn(c(t)||{},e)},error:(e,t,a)=>{let{normalizedError:n,extractedMetadata:o}=s(t),i={...a||{},...o,...n&&{error:{message:n.message,stack:n.stack,name:n.name}}};r.error(i,e)},fatal:(e,t,a)=>{let{normalizedError:n,extractedMetadata:o}=s(t),i={...a||{},...o,...n&&{error:{message:n.message,stack:n.stack,name:n.name}}};r.fatal(i,e)}}},child:function(e){return this.createChild(e)}}},46029:(e,r,t)=>{t.d(r,{Ag:()=>u,Al:()=>d,Cf:()=>c,DI:()=>l,Xf:()=>o,cb:()=>i}),t(30468);var a=t(31059);let n={PRO_MONTHLY:"price_1RWHbARrKLXtzZkME498Zuab",PRO_ANNUAL:"price_1RWHckRrKLXtzZkMLLn1vFvh"},o={FREE:"free",PRO_MONTHLY:"pro_monthly",PRO_ANNUAL:"pro_annual"},i={[o.FREE]:50,[o.PRO_MONTHLY]:500,[o.PRO_ANNUAL]:1e3},s=process.env.STRIPE_SECRET_KEY||"",c=process.env.STRIPE_WEBHOOK_SECRET||"",u=s?new a.Z(s,{apiVersion:"2023-10-16",appInfo:{name:"Excel Copilot",version:"1.0.0"}}):null;function d(e){switch(e){case o.PRO_MONTHLY:return n.PRO_MONTHLY;case o.PRO_ANNUAL:return n.PRO_ANNUAL;default:return n.PRO_MONTHLY}}function l(e){switch(e){case"active":case"trialing":return"active";case"canceled":case"unpaid":case"incomplete_expired":return"canceled";case"past_due":return"past_due";case"incomplete":return"incomplete";default:return"unknown"}}},63841:(e,r,t)=>{t.d(r,{P:()=>c,prisma:()=>s});var a=t(53524);let n={info:(e,...r)=>{},error:(e,...r)=>{console.error(`[DB ERROR] ${e}`,...r)},warn:(e,...r)=>{console.warn(`[DB WARNING] ${e}`,...r)}},o={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},i=[],s=global.prisma||new a.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function c(){return{...o,activeConnections:Math.min(Math.floor(5*Math.random())+1,o.maxPoolSize),poolSize:o.poolSize}}async function u(){try{await s.$disconnect(),n.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){n.error("Erro ao desconectar do banco de dados",e)}}s.$on("query",e=>{o.totalQueries++,e.duration&&(i.push(e.duration),i.length>100&&i.shift(),o.averageQueryTime=i.reduce((e,r)=>e+r,0)/i.length),e.duration&&e.duration>500&&n.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),s.$on("error",e=>{o.failedQueries++,o.connectionFailures++,o.lastConnectionFailure=new Date().toISOString(),n.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{u()})},30468:()=>{var e,r="https://js.stripe.com",t="".concat(r,"/").concat("basil","/stripe.js"),a=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,n=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,o=function(){for(var e=document.querySelectorAll('script[src^="'.concat(r,'"]')),t=0;t<e.length;t++){var o,i=e[t];if(o=i.src,a.test(o)||n.test(o))return i}return null},i=function(e){var r=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",a=document.createElement("script");a.src="".concat(t).concat(r);var n=document.head||document.body;if(!n)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return n.appendChild(a),a},s=null,c=null,u=null;Promise.resolve().then(function(){return e||(e=(null!==s?s:(s=new Promise(function(e,r){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var t,a=o();a?a&&null!==u&&null!==c&&(a.removeEventListener("load",u),a.removeEventListener("error",c),null===(t=a.parentNode)||void 0===t||t.removeChild(a),a=i(null)):a=i(null),u=function(){window.Stripe?e(window.Stripe):r(Error("Stripe.js not available"))},c=function(e){r(Error("Failed to load Stripe.js",{cause:e}))},a.addEventListener("load",u),a.addEventListener("error",c)}catch(e){r(e);return}})).catch(function(e){return s=null,Promise.reject(e)})).catch(function(r){return e=null,Promise.reject(r)}))}).catch(function(e){console.warn(e)})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,5972,9557,1059],()=>t(21528));module.exports=a})();