/**
 * Sistema de Monitoramento e Métricas para IA
 * Coleta métricas de uso, performance, custos e qualidade
 */

import { ENV } from '@/config/unified-environment';
import { logger } from '@/lib/logger';
import { prisma } from '@/server/db/client';

export interface AIMetrics {
  // Métricas de uso
  usage: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    totalTokensUsed: number;
    requestsByModel: Record<string, number>;
    requestsByOperation: Record<string, number>;
  };
  
  // Métricas de performance
  performance: {
    averageLatency: number;
    p95Latency: number;
    p99Latency: number;
    errorRate: number;
    timeoutRate: number;
    cacheHitRate: number;
  };
  
  // Métricas de custo
  costs: {
    estimatedCostUSD: number;
    tokenCosts: {
      input: number;
      output: number;
      total: number;
    };
    costByModel: Record<string, number>;
    costByUser: Record<string, number>;
  };
  
  // Métricas de qualidade
  quality: {
    userSatisfactionScore: number;
    feedbackCount: number;
    positiveRatings: number;
    negativeRatings: number;
    averageResponseLength: number;
  };
}

export interface AIRequestMetrics {
  userId: string;
  model: string;
  operationType: string;
  prompt: string;
  response: string;
  tokensUsed: {
    input: number;
    output: number;
    total: number;
  };
  responseTime: number;
  success: boolean;
  errorType?: string;
  errorMessage: string | undefined;
  cacheHit: boolean;
  workbookId: string | undefined;
  timestamp: Date;
}

export interface AICostConfig {
  models: Record<string, {
    inputTokenCost: number; // Custo por 1K tokens de input
    outputTokenCost: number; // Custo por 1K tokens de output
  }>;
}

/**
 * Sistema de Monitoramento de IA
 */
export class AIMonitoring {
  private static instance: AIMonitoring;
  private metrics: AIMetrics;
  private costConfig: AICostConfig;
  private recentRequests: AIRequestMetrics[] = [];
  private readonly MAX_RECENT_REQUESTS = 1000;

  private constructor() {
    this.costConfig = {
      models: {
        'gemini-1.5-pro': {
          inputTokenCost: 0.00125, // $1.25 per 1K input tokens
          outputTokenCost: 0.005, // $5.00 per 1K output tokens
        },
        'gemini-2.0-flash-001': {
          inputTokenCost: 0.000075, // $0.075 per 1K input tokens
          outputTokenCost: 0.0003, // $0.30 per 1K output tokens
        },
      },
    };

    this.metrics = this.initializeMetrics();
    this.startPeriodicReporting();
  }

  public static getInstance(): AIMonitoring {
    if (!AIMonitoring.instance) {
      AIMonitoring.instance = new AIMonitoring();
    }
    return AIMonitoring.instance;
  }

  /**
   * Inicializa métricas vazias
   */
  private initializeMetrics(): AIMetrics {
    return {
      usage: {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageResponseTime: 0,
        totalTokensUsed: 0,
        requestsByModel: {},
        requestsByOperation: {},
      },
      performance: {
        averageLatency: 0,
        p95Latency: 0,
        p99Latency: 0,
        errorRate: 0,
        timeoutRate: 0,
        cacheHitRate: 0,
      },
      costs: {
        estimatedCostUSD: 0,
        tokenCosts: {
          input: 0,
          output: 0,
          total: 0,
        },
        costByModel: {},
        costByUser: {},
      },
      quality: {
        userSatisfactionScore: 0,
        feedbackCount: 0,
        positiveRatings: 0,
        negativeRatings: 0,
        averageResponseLength: 0,
      },
    };
  }

  /**
   * Registra uma requisição de IA
   */
  public async recordRequest(requestMetrics: AIRequestMetrics): Promise<void> {
    try {
      // Adicionar à lista de requisições recentes
      this.recentRequests.push(requestMetrics);
      if (this.recentRequests.length > this.MAX_RECENT_REQUESTS) {
        this.recentRequests.shift();
      }

      // Atualizar métricas em tempo real
      this.updateMetrics(requestMetrics);

      // Persistir no banco de dados (comentado temporariamente devido a incompatibilidade de schema)
      // await this.persistMetrics(requestMetrics);

      // Log para debugging
      logger.debug('Métricas de IA registradas:', {
        userId: requestMetrics.userId,
        model: requestMetrics.model,
        operationType: requestMetrics.operationType,
        responseTime: requestMetrics.responseTime,
        tokensUsed: requestMetrics.tokensUsed.total,
        success: requestMetrics.success,
      });

    } catch (error) {
      logger.error('Erro ao registrar métricas de IA:', error);
    }
  }

  /**
   * Atualiza métricas em memória
   */
  private updateMetrics(request: AIRequestMetrics): void {
    const { usage, performance, costs } = this.metrics;

    // Atualizar métricas de uso
    usage.totalRequests++;
    if (request.success) {
      usage.successfulRequests++;
    } else {
      usage.failedRequests++;
    }

    usage.totalTokensUsed += request.tokensUsed.total;
    usage.requestsByModel[request.model] = (usage.requestsByModel[request.model] || 0) + 1;
    usage.requestsByOperation[request.operationType] = (usage.requestsByOperation[request.operationType] || 0) + 1;

    // Atualizar tempo médio de resposta
    usage.averageResponseTime = (usage.averageResponseTime * (usage.totalRequests - 1) + request.responseTime) / usage.totalRequests;

    // Atualizar métricas de performance
    this.updatePerformanceMetrics();

    // Atualizar métricas de custo
    this.updateCostMetrics(request);
  }

  /**
   * Atualiza métricas de performance
   */
  private updatePerformanceMetrics(): void {
    if (this.recentRequests.length === 0) return;

    const { performance } = this.metrics;
    const responseTimes = this.recentRequests.map(r => r.responseTime).sort((a, b) => a - b);
    const cacheHits = this.recentRequests.filter(r => r.cacheHit).length;
    const failures = this.recentRequests.filter(r => !r.success).length;

    // Calcular latências
    performance.averageLatency = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    performance.p95Latency = responseTimes[Math.floor(responseTimes.length * 0.95)] || 0;
    performance.p99Latency = responseTimes[Math.floor(responseTimes.length * 0.99)] || 0;

    // Calcular taxas
    performance.errorRate = failures / this.recentRequests.length;
    performance.cacheHitRate = cacheHits / this.recentRequests.length;
  }

  /**
   * Atualiza métricas de custo
   */
  private updateCostMetrics(request: AIRequestMetrics): void {
    const { costs } = this.metrics;
    const modelConfig = this.costConfig.models[request.model];

    if (!modelConfig) {
      logger.warn(`Configuração de custo não encontrada para modelo: ${request.model}`);
      return;
    }

    // Calcular custos
    const inputCost = (request.tokensUsed.input / 1000) * modelConfig.inputTokenCost;
    const outputCost = (request.tokensUsed.output / 1000) * modelConfig.outputTokenCost;
    const totalCost = inputCost + outputCost;

    // Atualizar custos totais
    costs.estimatedCostUSD += totalCost;
    costs.tokenCosts.input += inputCost;
    costs.tokenCosts.output += outputCost;
    costs.tokenCosts.total += totalCost;

    // Atualizar custos por modelo
    costs.costByModel[request.model] = (costs.costByModel[request.model] || 0) + totalCost;

    // Atualizar custos por usuário
    costs.costByUser[request.userId] = (costs.costByUser[request.userId] || 0) + totalCost;
  }

  /**
   * Persiste métricas no banco de dados
   * Comentado temporariamente devido a incompatibilidade de schema
   */
  private async persistMetrics(request: AIRequestMetrics): Promise<void> {
    try {
      // Schema do banco não tem os campos necessários ainda
      // await prisma.aiMetrics.create({
      //   data: {
      //     // Campos serão mapeados quando schema for atualizado
      //   },
      // });
      logger.debug('Métricas registradas (persistência temporariamente desabilitada)');
    } catch (error) {
      logger.error('Erro ao persistir métricas no banco:', error);
    }
  }

  /**
   * Registra feedback do usuário
   */
  public async recordFeedback(
    userId: string,
    requestId: string,
    rating: 'positive' | 'negative',
    comment?: string
  ): Promise<void> {
    try {
      // Temporariamente comentado devido a incompatibilidade de schema
      // await prisma.commandFeedback.create({
      //   data: {
      //     command: requestId,
      //     rating: rating === 'positive' ? 5 : 1,
      //     feedback: comment || '',
      //   },
      // });

      // Atualizar métricas de qualidade
      this.metrics.quality.feedbackCount++;
      if (rating === 'positive') {
        this.metrics.quality.positiveRatings++;
      } else {
        this.metrics.quality.negativeRatings++;
      }

      // Recalcular score de satisfação
      const totalRatings = this.metrics.quality.positiveRatings + this.metrics.quality.negativeRatings;
      this.metrics.quality.userSatisfactionScore = this.metrics.quality.positiveRatings / totalRatings;

    } catch (error) {
      logger.error('Erro ao registrar feedback:', error);
    }
  }

  /**
   * Obtém métricas atuais
   */
  public getMetrics(): AIMetrics {
    return { ...this.metrics };
  }

  /**
   * Obtém métricas de um período específico
   */
  public async getMetricsForPeriod(
    startDate: Date,
    endDate: Date,
    userId?: string
  ): Promise<Partial<AIMetrics>> {
    try {
      const whereClause: any = {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      };

      if (userId) {
        whereClause.userId = userId;
      }

      const records = await prisma.aiMetrics.findMany({
        where: whereClause,
      });

      // Processar registros e calcular métricas
      const metrics = this.calculateMetricsFromRecords(records);
      return metrics;

    } catch (error) {
      logger.error('Erro ao obter métricas do período:', error);
      return {};
    }
  }

  /**
   * Calcula métricas a partir de registros do banco
   */
  private calculateMetricsFromRecords(records: any[]): Partial<AIMetrics> {
    if (records.length === 0) return {};

    const totalRequests = records.length;
    const successfulRequests = records.filter(r => r.success).length;
    const totalTokens = records.reduce((sum, r) => sum + r.totalTokens, 0);
    const averageResponseTime = records.reduce((sum, r) => sum + r.responseTime, 0) / totalRequests;

    return {
      usage: {
        totalRequests,
        successfulRequests,
        failedRequests: totalRequests - successfulRequests,
        averageResponseTime,
        totalTokensUsed: totalTokens,
        requestsByModel: {},
        requestsByOperation: {},
      },
    };
  }

  /**
   * Inicia relatórios periódicos
   */
  private startPeriodicReporting(): void {
    if (ENV.IS_PRODUCTION) {
      // Relatório a cada hora
      setInterval(() => {
        this.generateHourlyReport();
      }, 60 * 60 * 1000);

      // Relatório diário
      setInterval(() => {
        this.generateDailyReport();
      }, 24 * 60 * 60 * 1000);
    }
  }

  /**
   * Gera relatório horário
   */
  private async generateHourlyReport(): Promise<void> {
    try {
      const metrics = this.getMetrics();
      logger.info('Relatório horário de IA:', {
        totalRequests: metrics.usage.totalRequests,
        successRate: (metrics.usage.successfulRequests / metrics.usage.totalRequests) * 100,
        averageResponseTime: metrics.performance.averageLatency,
        estimatedCost: metrics.costs.estimatedCostUSD,
        cacheHitRate: metrics.performance.cacheHitRate * 100,
      });
    } catch (error) {
      logger.error('Erro ao gerar relatório horário:', error);
    }
  }

  /**
   * Gera relatório diário
   */
  private async generateDailyReport(): Promise<void> {
    try {
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000);
      
      const dailyMetrics = await this.getMetricsForPeriod(startDate, endDate);
      
      logger.info('Relatório diário de IA:', dailyMetrics);
      
      // Aqui poderia enviar para serviços externos de monitoramento
      // como DataDog, New Relic, etc.
      
    } catch (error) {
      logger.error('Erro ao gerar relatório diário:', error);
    }
  }
}
