/**
 * Sistema de Autenticação de Dois Fatores (2FA)
 * Implementação básica para usuários administrativos
 */

import { randomBytes, createHash } from 'crypto';
import { logger } from '@/lib/logger';
import { prisma } from '@/server/db/client';

// Interface para configuração 2FA
export interface TwoFactorConfig {
  userId: string;
  secret: string;
  backupCodes: string[];
  isEnabled: boolean;
  lastUsed?: Date;
}

// Interface para verificação 2FA
export interface TwoFactorVerification {
  userId: string;
  code: string;
  type: 'totp' | 'backup' | 'email';
}

// Store em memória para códigos temporários (em produção, usar Redis)
const tempCodes = new Map<string, { code: string; expires: number; attempts: number }>();

/**
 * Gera um código de 6 dígitos para 2FA
 */
export function generateTwoFactorCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

/**
 * Gera códigos de backup para 2FA
 */
export function generateBackupCodes(count: number = 10): string[] {
  const codes: string[] = [];
  for (let i = 0; i < count; i++) {
    const code = randomBytes(4).toString('hex').toUpperCase();
    codes.push(code);
  }
  return codes;
}

/**
 * Gera um secret para TOTP (Time-based One-Time Password)
 */
export function generateTOTPSecret(): string {
  return randomBytes(20).toString('hex');
}

/**
 * Hash de um código de backup para armazenamento seguro
 */
export function hashBackupCode(code: string): string {
  return createHash('sha256').update(code).digest('hex');
}

/**
 * Verifica se um código de backup é válido
 */
export function verifyBackupCode(code: string, hashedCode: string): boolean {
  const inputHash = hashBackupCode(code);
  return inputHash === hashedCode;
}

/**
 * Envia código 2FA por email (implementação básica)
 */
export async function sendTwoFactorCodeByEmail(userId: string, email: string): Promise<boolean> {
  try {
    const code = generateTwoFactorCode();
    const expires = Date.now() + (5 * 60 * 1000); // 5 minutos
    
    // Armazenar código temporário
    tempCodes.set(userId, { code, expires, attempts: 0 });
    
    // Log para desenvolvimento (em produção, enviar email real)
    logger.info('📧 Código 2FA gerado', {
      userId,
      email: email.replace(/(.{2}).*(@.*)/, '$1***$2'), // Mascarar email
      code: ENV.NODE_ENV === 'development' ? code : '******', // Mostrar código apenas em dev
      expiresIn: '5 minutos'
    });
    
    // TODO: Implementar envio de email real
    // await emailService.sendTwoFactorCode(email, code);
    
    return true;
  } catch (error) {
    logger.error('Erro ao enviar código 2FA por email', { error, userId });
    return false;
  }
}

/**
 * Verifica código 2FA temporário
 */
export async function verifyTwoFactorCode(userId: string, inputCode: string): Promise<boolean> {
  try {
    const stored = tempCodes.get(userId);
    
    if (!stored) {
      logger.warn('Tentativa de verificação 2FA sem código armazenado', { userId });
      return false;
    }
    
    // Verificar se expirou
    if (Date.now() > stored.expires) {
      tempCodes.delete(userId);
      logger.warn('Código 2FA expirado', { userId });
      return false;
    }
    
    // Incrementar tentativas
    stored.attempts++;
    
    // Limitar tentativas
    if (stored.attempts > 3) {
      tempCodes.delete(userId);
      logger.warn('Muitas tentativas de verificação 2FA', { userId, attempts: stored.attempts });
      return false;
    }
    
    // Verificar código
    if (stored.code === inputCode) {
      tempCodes.delete(userId);
      logger.info('✅ Código 2FA verificado com sucesso', { userId });
      return true;
    }
    
    logger.warn('Código 2FA inválido', { userId, attempts: stored.attempts });
    return false;
  } catch (error) {
    logger.error('Erro ao verificar código 2FA', { error, userId });
    return false;
  }
}

/**
 * Habilita 2FA para um usuário
 */
export async function enableTwoFactorForUser(userId: string): Promise<{ secret: string; backupCodes: string[] } | null> {
  try {
    const secret = generateTOTPSecret();
    const backupCodes = generateBackupCodes();
    const hashedBackupCodes = backupCodes.map(code => hashBackupCode(code));
    
    // Salvar configuração no banco de dados
    // TODO: Implementar tabela TwoFactorConfig no schema Prisma
    /*
    await prisma.twoFactorConfig.upsert({
      where: { userId },
      update: {
        secret,
        backupCodes: hashedBackupCodes,
        isEnabled: true,
        updatedAt: new Date(),
      },
      create: {
        userId,
        secret,
        backupCodes: hashedBackupCodes,
        isEnabled: true,
      },
    });
    */
    
    logger.info('✅ 2FA habilitado para usuário', { userId });
    
    return { secret, backupCodes };
  } catch (error) {
    logger.error('Erro ao habilitar 2FA', { error, userId });
    return null;
  }
}

/**
 * Desabilita 2FA para um usuário
 */
export async function disableTwoFactorForUser(userId: string): Promise<boolean> {
  try {
    // TODO: Implementar desabilitação no banco de dados
    /*
    await prisma.twoFactorConfig.update({
      where: { userId },
      data: {
        isEnabled: false,
        updatedAt: new Date(),
      },
    });
    */
    
    // Limpar códigos temporários
    tempCodes.delete(userId);
    
    logger.info('✅ 2FA desabilitado para usuário', { userId });
    return true;
  } catch (error) {
    logger.error('Erro ao desabilitar 2FA', { error, userId });
    return false;
  }
}

/**
 * Verifica se um usuário tem 2FA habilitado
 */
export async function isTwoFactorEnabled(userId: string): Promise<boolean> {
  try {
    // TODO: Implementar verificação no banco de dados
    /*
    const config = await prisma.twoFactorConfig.findUnique({
      where: { userId },
      select: { isEnabled: true },
    });
    
    return config?.isEnabled || false;
    */
    
    // Por enquanto, retornar false (2FA não obrigatório)
    return false;
  } catch (error) {
    logger.error('Erro ao verificar status 2FA', { error, userId });
    return false;
  }
}

/**
 * Verifica se um usuário precisa de 2FA (usuários admin)
 */
export async function requiresTwoFactor(userId: string): Promise<boolean> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, email: true },
    });

    // Por enquanto, não exigir 2FA para ninguém (campo isAdmin não existe no schema)
    // TODO: Adicionar campo isAdmin ao schema Prisma quando necessário
    return false;
  } catch (error) {
    logger.error('Erro ao verificar necessidade de 2FA', { error, userId });
    return false;
  }
}

/**
 * Middleware para verificar 2FA em rotas sensíveis
 */
export async function verifyTwoFactorMiddleware(userId: string, skipFor?: string[]): Promise<boolean> {
  try {
    // Verificar se o usuário precisa de 2FA
    const needsTwoFactor = await requiresTwoFactor(userId);
    
    if (!needsTwoFactor) {
      return true; // Não precisa de 2FA
    }
    
    // Verificar se 2FA está habilitado
    const isEnabled = await isTwoFactorEnabled(userId);
    
    if (!isEnabled) {
      logger.warn('Usuário admin sem 2FA habilitado', { userId });
      return false; // Admin deve ter 2FA habilitado
    }
    
    // TODO: Implementar verificação de sessão 2FA
    // Por enquanto, permitir acesso
    return true;
  } catch (error) {
    logger.error('Erro no middleware 2FA', { error, userId });
    return false;
  }
}

// Importar ENV para uso no código
import { ENV } from '@/config/unified-environment';
